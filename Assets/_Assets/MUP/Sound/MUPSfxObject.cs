using Cysharp.Threading.Tasks;
using MUP.Core;
using UnityEngine;

namespace MUP.Sound
{
    public class MUPSfxObject : MonoBehaviour, IPoolableObject
    {
        public AudioSource audioSource;

        public void OnObjectCreate()
        {

        }

        public void OnObjectGet()
        {
            gameObject.SetActive(true);
        }

        public void OnObjectRelease()
        {
            gameObject.SetActive(false);
        }

        public void OnObjectDestroy()
        {
            Destroy(gameObject);
        }

        public async UniTaskVoid PlaySfx(AudioClip audioClip)
        {
            audioSource.clip = audioClip;
            audioSource.Play();

            var ct = this.GetCancellationTokenOnDestroy();

            while (audioSource.isPlaying)
            {
                await UniTask.Delay(1000, cancellationToken: ct);
            }

            PoolManager.Instance.Release(EPoolType.SfxObject, gameObject);
        }
    }
}