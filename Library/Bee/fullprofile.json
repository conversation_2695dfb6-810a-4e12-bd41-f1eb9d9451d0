{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 66700, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 66700, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 66700, "tid": 2487, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 66700, "tid": 2487, "ts": 1752353586160681, "dur": 496, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 66700, "tid": 2487, "ts": 1752353586163976, "dur": 1022, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 66700, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 66700, "tid": 1, "ts": 1752353585747853, "dur": 7081, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 66700, "tid": 1, "ts": 1752353585754939, "dur": 60595, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 66700, "tid": 1, "ts": 1752353585815543, "dur": 44820, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 66700, "tid": 2487, "ts": 1752353586165004, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 66700, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585746196, "dur": 4208, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585750408, "dur": 403468, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585752608, "dur": 3637, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585756249, "dur": 1377, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585757629, "dur": 11162, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585768854, "dur": 549, "ph": "X", "name": "ProcessMessages 8052", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585769409, "dur": 76, "ph": "X", "name": "ReadAsync 8052", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585769489, "dur": 6, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585769496, "dur": 92, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585769603, "dur": 2, "ph": "X", "name": "ProcessMessages 1592", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585769606, "dur": 30, "ph": "X", "name": "ReadAsync 1592", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585769696, "dur": 6, "ph": "X", "name": "ProcessMessages 1760", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585769711, "dur": 182, "ph": "X", "name": "ReadAsync 1760", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585769936, "dur": 28, "ph": "X", "name": "ProcessMessages 3857", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585769967, "dur": 122, "ph": "X", "name": "ReadAsync 3857", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585770093, "dur": 6, "ph": "X", "name": "ProcessMessages 5824", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585770102, "dur": 139, "ph": "X", "name": "ReadAsync 5824", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585770243, "dur": 2, "ph": "X", "name": "ProcessMessages 1901", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585770246, "dur": 193, "ph": "X", "name": "ReadAsync 1901", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585770442, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585770444, "dur": 106, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585770552, "dur": 4, "ph": "X", "name": "ProcessMessages 4774", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585770561, "dur": 260, "ph": "X", "name": "ReadAsync 4774", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585770868, "dur": 2557, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585773429, "dur": 6, "ph": "X", "name": "ProcessMessages 8172", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585773438, "dur": 88, "ph": "X", "name": "ReadAsync 8172", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585773527, "dur": 2, "ph": "X", "name": "ProcessMessages 2004", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585773531, "dur": 66, "ph": "X", "name": "ReadAsync 2004", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585773604, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585773609, "dur": 38, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585773650, "dur": 155, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585773806, "dur": 483, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585774316, "dur": 5, "ph": "X", "name": "ProcessMessages 6556", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585774322, "dur": 42, "ph": "X", "name": "ReadAsync 6556", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585774367, "dur": 1, "ph": "X", "name": "ProcessMessages 1276", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585776776, "dur": 32, "ph": "X", "name": "ReadAsync 1276", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585776897, "dur": 6, "ph": "X", "name": "ProcessMessages 8114", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585776905, "dur": 52, "ph": "X", "name": "ReadAsync 8114", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585776958, "dur": 2, "ph": "X", "name": "ProcessMessages 3202", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585776965, "dur": 49, "ph": "X", "name": "ReadAsync 3202", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777015, "dur": 1, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777017, "dur": 24, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777045, "dur": 1, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777051, "dur": 38, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777094, "dur": 1, "ph": "X", "name": "ProcessMessages 1177", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777097, "dur": 36, "ph": "X", "name": "ReadAsync 1177", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777134, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777136, "dur": 81, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777231, "dur": 1, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777232, "dur": 26, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777260, "dur": 1, "ph": "X", "name": "ProcessMessages 1623", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777262, "dur": 27, "ph": "X", "name": "ReadAsync 1623", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777290, "dur": 1, "ph": "X", "name": "ProcessMessages 1148", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777292, "dur": 33, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777328, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777330, "dur": 34, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777365, "dur": 1, "ph": "X", "name": "ProcessMessages 1226", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777367, "dur": 20, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777388, "dur": 5, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777394, "dur": 30, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777427, "dur": 33, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777461, "dur": 1, "ph": "X", "name": "ProcessMessages 1357", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777463, "dur": 22, "ph": "X", "name": "ReadAsync 1357", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777488, "dur": 58, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777549, "dur": 28, "ph": "X", "name": "ProcessMessages 1696", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777578, "dur": 27, "ph": "X", "name": "ReadAsync 1696", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777608, "dur": 1, "ph": "X", "name": "ProcessMessages 1648", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777610, "dur": 192, "ph": "X", "name": "ReadAsync 1648", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777803, "dur": 1, "ph": "X", "name": "ProcessMessages 2149", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777806, "dur": 31, "ph": "X", "name": "ReadAsync 2149", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777842, "dur": 5, "ph": "X", "name": "ProcessMessages 4398", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777848, "dur": 40, "ph": "X", "name": "ReadAsync 4398", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777892, "dur": 1, "ph": "X", "name": "ProcessMessages 1690", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585777895, "dur": 70, "ph": "X", "name": "ReadAsync 1690", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778013, "dur": 1, "ph": "X", "name": "ProcessMessages 1104", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778018, "dur": 32, "ph": "X", "name": "ReadAsync 1104", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778058, "dur": 3, "ph": "X", "name": "ProcessMessages 3231", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778063, "dur": 69, "ph": "X", "name": "ReadAsync 3231", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778143, "dur": 340, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778484, "dur": 2, "ph": "X", "name": "ProcessMessages 1980", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778488, "dur": 106, "ph": "X", "name": "ReadAsync 1980", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778604, "dur": 1, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778606, "dur": 123, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778735, "dur": 1, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778742, "dur": 24, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778767, "dur": 2, "ph": "X", "name": "ProcessMessages 3023", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778770, "dur": 24, "ph": "X", "name": "ReadAsync 3023", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778796, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778797, "dur": 40, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778841, "dur": 67, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778910, "dur": 1, "ph": "X", "name": "ProcessMessages 1113", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778911, "dur": 77, "ph": "X", "name": "ReadAsync 1113", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585778990, "dur": 11, "ph": "X", "name": "ProcessMessages 1589", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779012, "dur": 28, "ph": "X", "name": "ReadAsync 1589", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779049, "dur": 1, "ph": "X", "name": "ProcessMessages 1592", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779051, "dur": 38, "ph": "X", "name": "ReadAsync 1592", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779094, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779096, "dur": 28, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779130, "dur": 1, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779131, "dur": 30, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779164, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779166, "dur": 17, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779184, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779185, "dur": 21, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779211, "dur": 46, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779266, "dur": 1, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779268, "dur": 27, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779298, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779300, "dur": 35, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779336, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779339, "dur": 22, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779368, "dur": 38, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779407, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779409, "dur": 42, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779457, "dur": 1, "ph": "X", "name": "ProcessMessages 1591", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779480, "dur": 34, "ph": "X", "name": "ReadAsync 1591", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779522, "dur": 1, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779524, "dur": 27, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779557, "dur": 1, "ph": "X", "name": "ProcessMessages 1675", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779560, "dur": 38, "ph": "X", "name": "ReadAsync 1675", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779599, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779601, "dur": 81, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779683, "dur": 2, "ph": "X", "name": "ProcessMessages 2161", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779685, "dur": 35, "ph": "X", "name": "ReadAsync 2161", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779723, "dur": 1, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779725, "dur": 30, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779756, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779758, "dur": 33, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779793, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779794, "dur": 23, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779825, "dur": 24, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779850, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779854, "dur": 73, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779928, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779929, "dur": 30, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585779991, "dur": 37, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780029, "dur": 12, "ph": "X", "name": "ProcessMessages 3084", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780060, "dur": 26, "ph": "X", "name": "ReadAsync 3084", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780097, "dur": 34, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780132, "dur": 1, "ph": "X", "name": "ProcessMessages 1215", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780154, "dur": 18, "ph": "X", "name": "ReadAsync 1215", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780173, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780175, "dur": 25, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780257, "dur": 24, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780282, "dur": 6, "ph": "X", "name": "ProcessMessages 1984", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780289, "dur": 68, "ph": "X", "name": "ReadAsync 1984", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780358, "dur": 1, "ph": "X", "name": "ProcessMessages 1849", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780361, "dur": 23, "ph": "X", "name": "ReadAsync 1849", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780386, "dur": 3, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780391, "dur": 16, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780415, "dur": 50, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780471, "dur": 1, "ph": "X", "name": "ProcessMessages 1443", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780473, "dur": 22, "ph": "X", "name": "ReadAsync 1443", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780501, "dur": 20, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780543, "dur": 17, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780564, "dur": 1, "ph": "X", "name": "ProcessMessages 1379", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780565, "dur": 33, "ph": "X", "name": "ReadAsync 1379", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780599, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780601, "dur": 24, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780628, "dur": 64, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780697, "dur": 1, "ph": "X", "name": "ProcessMessages 2096", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780699, "dur": 18, "ph": "X", "name": "ReadAsync 2096", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780718, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780721, "dur": 25, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780747, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780749, "dur": 55, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780813, "dur": 3, "ph": "X", "name": "ProcessMessages 1750", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780816, "dur": 26, "ph": "X", "name": "ReadAsync 1750", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780844, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780846, "dur": 78, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780925, "dur": 1, "ph": "X", "name": "ProcessMessages 1379", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780930, "dur": 43, "ph": "X", "name": "ReadAsync 1379", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585780978, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781004, "dur": 33, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781038, "dur": 1, "ph": "X", "name": "ProcessMessages 1344", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781040, "dur": 40, "ph": "X", "name": "ReadAsync 1344", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781081, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781083, "dur": 39, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781123, "dur": 1, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781125, "dur": 23, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781150, "dur": 4, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781154, "dur": 21, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781178, "dur": 269, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781450, "dur": 173, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781632, "dur": 205, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781849, "dur": 20, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781870, "dur": 119, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585781993, "dur": 2, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585782000, "dur": 25, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585782026, "dur": 1, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585782069, "dur": 35, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585782125, "dur": 646, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585782773, "dur": 3, "ph": "X", "name": "ProcessMessages 2118", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585782881, "dur": 115, "ph": "X", "name": "ReadAsync 2118", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585782997, "dur": 21, "ph": "X", "name": "ProcessMessages 2079", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585783019, "dur": 520, "ph": "X", "name": "ReadAsync 2079", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585783543, "dur": 143, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585783691, "dur": 1402, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585785097, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585785099, "dur": 32, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585785141, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585785151, "dur": 515, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585785669, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585785670, "dur": 148, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585785821, "dur": 19, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585785841, "dur": 371, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585786213, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585786215, "dur": 22, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585786239, "dur": 506, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585786770, "dur": 196, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585786979, "dur": 28413, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585815396, "dur": 8, "ph": "X", "name": "ProcessMessages 8185", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585815405, "dur": 29, "ph": "X", "name": "ReadAsync 8185", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585815436, "dur": 328, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585815767, "dur": 27, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585815796, "dur": 204, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816003, "dur": 216, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816225, "dur": 41, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816267, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816269, "dur": 39, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816310, "dur": 1, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816312, "dur": 20, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816335, "dur": 24, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816360, "dur": 5, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816366, "dur": 64, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816433, "dur": 20, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816457, "dur": 26, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816485, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816487, "dur": 43, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816531, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816533, "dur": 263, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816798, "dur": 1, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816799, "dur": 26, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816827, "dur": 26, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816855, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816861, "dur": 20, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585816884, "dur": 265, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817150, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817152, "dur": 27, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817181, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817183, "dur": 24, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817208, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817209, "dur": 31, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817242, "dur": 295, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817544, "dur": 2, "ph": "X", "name": "ProcessMessages 2137", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817547, "dur": 28, "ph": "X", "name": "ReadAsync 2137", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585817577, "dur": 656, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585818234, "dur": 1, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585818236, "dur": 39, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585818277, "dur": 1, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585818279, "dur": 420, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585818701, "dur": 1, "ph": "X", "name": "ProcessMessages 1395", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585818703, "dur": 61, "ph": "X", "name": "ReadAsync 1395", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585818765, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585818769, "dur": 745, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585819517, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585819519, "dur": 132, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585819652, "dur": 1, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585819654, "dur": 32, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585819687, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585819689, "dur": 43, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585819738, "dur": 420, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585820160, "dur": 160, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585820323, "dur": 24, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585820348, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585820350, "dur": 47, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585820399, "dur": 474, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585820876, "dur": 29, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585820907, "dur": 517, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585821426, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585821428, "dur": 518, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585821959, "dur": 156, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585822120, "dur": 616, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585822740, "dur": 42, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585822783, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585822785, "dur": 503, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585823289, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585823291, "dur": 513, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585823806, "dur": 24, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585823833, "dur": 1, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585823834, "dur": 51, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585823886, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585823888, "dur": 22, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585823912, "dur": 522, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585824437, "dur": 25, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585824464, "dur": 550, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585825016, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585825023, "dur": 20, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585825045, "dur": 38, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585825085, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585825087, "dur": 554, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585825644, "dur": 26, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585825672, "dur": 216, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585825897, "dur": 25, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585825925, "dur": 467, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585826394, "dur": 1, "ph": "X", "name": "ProcessMessages 1339", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585826396, "dur": 30, "ph": "X", "name": "ReadAsync 1339", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585826427, "dur": 1, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585826428, "dur": 50, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585826480, "dur": 471, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585826954, "dur": 39, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585826999, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585827001, "dur": 36, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585827038, "dur": 1, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585827040, "dur": 471, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585827513, "dur": 26, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585827544, "dur": 510, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585828056, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585828057, "dur": 489, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585828549, "dur": 4, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585828564, "dur": 18, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585828584, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585828586, "dur": 506, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829094, "dur": 19, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829116, "dur": 577, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829694, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829695, "dur": 31, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829732, "dur": 132, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829867, "dur": 31, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829899, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829901, "dur": 19, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829927, "dur": 22, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585829951, "dur": 626, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585830580, "dur": 17, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585830599, "dur": 600, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585831200, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585831202, "dur": 539, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585831742, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585831743, "dur": 484, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585832230, "dur": 35, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585832267, "dur": 540, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585832811, "dur": 146, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585832959, "dur": 470, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585833457, "dur": 28, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585833491, "dur": 758, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585834261, "dur": 153, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585834416, "dur": 496, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585834914, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585834915, "dur": 870, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585835787, "dur": 2, "ph": "X", "name": "ProcessMessages 2484", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585835790, "dur": 443, "ph": "X", "name": "ReadAsync 2484", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585836235, "dur": 248, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585836485, "dur": 1, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585836487, "dur": 232, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585836720, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585836722, "dur": 190, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585836915, "dur": 167, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585837200, "dur": 23, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585837226, "dur": 134, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585837362, "dur": 19, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585837384, "dur": 239, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585837625, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585837627, "dur": 1324, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585838952, "dur": 5, "ph": "X", "name": "ProcessMessages 5915", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585838958, "dur": 27, "ph": "X", "name": "ReadAsync 5915", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585838993, "dur": 1, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585838994, "dur": 29, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585839024, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585839026, "dur": 43, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585839071, "dur": 530, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585839706, "dur": 268, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585839976, "dur": 143, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840120, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840122, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840202, "dur": 169, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840378, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840380, "dur": 84, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840467, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840621, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840681, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840765, "dur": 120, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585840887, "dur": 113, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841003, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841035, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841125, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841178, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841184, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841331, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841366, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841496, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841609, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841611, "dur": 125, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841739, "dur": 84, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841829, "dur": 120, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841951, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841952, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585841992, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842072, "dur": 164, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842238, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842253, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842332, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842420, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842440, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842444, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842515, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842587, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842616, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842703, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842772, "dur": 47, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842821, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842894, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585842979, "dur": 241, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843222, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843225, "dur": 33, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843260, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843450, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843452, "dur": 102, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843557, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843620, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843622, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843690, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843768, "dur": 106, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843886, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585843928, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585844049, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585844054, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585844076, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585844135, "dur": 225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585844362, "dur": 295, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585844659, "dur": 133, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585844794, "dur": 163, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585844965, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585845004, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585845007, "dur": 264, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585845278, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585845475, "dur": 144, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585845622, "dur": 235, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585845859, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585845906, "dur": 27, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585845935, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585845993, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585846074, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585846220, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585846244, "dur": 101, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585846352, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585846514, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585846522, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585846730, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585846733, "dur": 56, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585846796, "dur": 334, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847140, "dur": 210, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847352, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847361, "dur": 94, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847516, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847526, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847631, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847633, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847663, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847664, "dur": 63, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847730, "dur": 37, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847768, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847770, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847795, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585847837, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848003, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848024, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848104, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848270, "dur": 8, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848281, "dur": 95, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848394, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848402, "dur": 302, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848707, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848720, "dur": 65, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848867, "dur": 10, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585848879, "dur": 193, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849074, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849076, "dur": 254, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849334, "dur": 143, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849496, "dur": 159, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849670, "dur": 3, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849682, "dur": 34, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849767, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849822, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849824, "dur": 77, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585849902, "dur": 31, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850002, "dur": 175, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850196, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850198, "dur": 90, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850302, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850304, "dur": 152, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850461, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850599, "dur": 137, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850741, "dur": 29, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850777, "dur": 58, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850862, "dur": 16, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585850880, "dur": 4958, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585855844, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585855852, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585855917, "dur": 3, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585855922, "dur": 551, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585856476, "dur": 1551, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585858029, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585858031, "dur": 546, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585858580, "dur": 233, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585858815, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585858985, "dur": 644, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585859630, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585859842, "dur": 259, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585860103, "dur": 304, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585860409, "dur": 248, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585860660, "dur": 812, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585861475, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585861693, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585861694, "dur": 455, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585862152, "dur": 402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585862720, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585862747, "dur": 146, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585862895, "dur": 568, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585863466, "dur": 250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585863717, "dur": 13, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585863737, "dur": 3205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585866945, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585866947, "dur": 373, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585867322, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585867498, "dur": 281, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585867782, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585867844, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585868025, "dur": 578, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585868604, "dur": 1187, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585869794, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585869796, "dur": 1300, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585871102, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585871107, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585871255, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585871257, "dur": 221, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585871480, "dur": 359, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585871849, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585871973, "dur": 324, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585872299, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585872301, "dur": 742, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585873045, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585873128, "dur": 728, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585873863, "dur": 688, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585874555, "dur": 773, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585875331, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585875401, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585875483, "dur": 325, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585875810, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585876012, "dur": 1025, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585877040, "dur": 235, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585877286, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585877501, "dur": 126, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585877628, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585877664, "dur": 363, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585878029, "dur": 2592, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585880624, "dur": 247, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585880873, "dur": 324, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585881200, "dur": 214, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585881417, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585881595, "dur": 683, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585882280, "dur": 1036, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585883319, "dur": 251, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585883571, "dur": 22, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585883595, "dur": 103, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585883701, "dur": 274, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585883983, "dur": 658, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585884643, "dur": 356, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585885002, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585885033, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585885101, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585885166, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585885309, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585885311, "dur": 230, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585885543, "dur": 348, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585885893, "dur": 214, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585886110, "dur": 368, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585886480, "dur": 297, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585886780, "dur": 377, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585887160, "dur": 1949, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585889111, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585889231, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585889386, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585889565, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585889777, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585889793, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585889939, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585890131, "dur": 32, "ph": "X", "name": "ReadAsync 7", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585890165, "dur": 103, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585890269, "dur": 548, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585890819, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585890947, "dur": 831, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585891784, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585891787, "dur": 206, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585891998, "dur": 386, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585892387, "dur": 470, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585892860, "dur": 238, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585893101, "dur": 455, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585893559, "dur": 445, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585894007, "dur": 574, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585894584, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585894734, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585894854, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585894954, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585895063, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585895190, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585895225, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585895322, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585895394, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585895498, "dur": 2299, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585897801, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585897803, "dur": 427, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585898233, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585898235, "dur": 913, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585899151, "dur": 978, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585900135, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585900247, "dur": 288, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585900539, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585900716, "dur": 1487, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585902206, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585902323, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585902395, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585902675, "dur": 4267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585906944, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585906946, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585907023, "dur": 438, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585907464, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585907553, "dur": 1460, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585909015, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585909023, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585909160, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585909361, "dur": 3857, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585913222, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585913226, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585913294, "dur": 947, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585914247, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585914252, "dur": 378, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585914639, "dur": 331, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585914973, "dur": 139, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585915115, "dur": 184, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585915301, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585915366, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585915453, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585915503, "dur": 469, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585915975, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585916033, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585916078, "dur": 268, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585916354, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585916453, "dur": 387, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585916842, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585916844, "dur": 53702, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585970553, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585970556, "dur": 65, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585970628, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585970633, "dur": 140, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585970776, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585970823, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585970880, "dur": 47, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585970930, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585970969, "dur": 1065, "ph": "X", "name": "ProcessMessages 2454", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585972037, "dur": 3314, "ph": "X", "name": "ReadAsync 2454", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585975371, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585975377, "dur": 225, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585975605, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585975608, "dur": 868, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585976480, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585976482, "dur": 1722, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585978208, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585978341, "dur": 173, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585978521, "dur": 430, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585978955, "dur": 537, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585979497, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585979501, "dur": 878, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585980382, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585980385, "dur": 1207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585981595, "dur": 1059, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585982657, "dur": 488, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585983148, "dur": 1849, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585985000, "dur": 652, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585985658, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585985662, "dur": 487, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585986152, "dur": 575, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585986731, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585986907, "dur": 1176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585988088, "dur": 1209, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585989299, "dur": 418, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585989721, "dur": 283, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585990007, "dur": 329, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585990338, "dur": 1096, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585991437, "dur": 914, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585992354, "dur": 1074, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585993435, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585993440, "dur": 1269, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585994713, "dur": 2210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585996949, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585996951, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585997043, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585997047, "dur": 1012, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585998065, "dur": 15, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585998085, "dur": 730, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585998819, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585998821, "dur": 525, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585999361, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353585999365, "dur": 1592, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586000960, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586000961, "dur": 1213, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586002181, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586002186, "dur": 79, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586002269, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586002455, "dur": 249, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586002707, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586002711, "dur": 2034, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586004750, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586004753, "dur": 2260, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586007022, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586007028, "dur": 899, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586007932, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586007934, "dur": 181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586008122, "dur": 17, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586008141, "dur": 1698, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586009855, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586009858, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586009905, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010121, "dur": 192, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010315, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010317, "dur": 95, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010416, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010488, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010537, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010567, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010630, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010704, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586010788, "dur": 215, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011005, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011007, "dur": 163, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011171, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011172, "dur": 68, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011243, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011375, "dur": 224, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011608, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011613, "dur": 97, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011713, "dur": 54, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011769, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011901, "dur": 95, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586011999, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012069, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012071, "dur": 145, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012224, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012265, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012420, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012424, "dur": 78, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012504, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012506, "dur": 59, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012567, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012633, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012708, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012809, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012993, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586012995, "dur": 46, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013043, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013186, "dur": 79, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013268, "dur": 60, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013330, "dur": 95, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013428, "dur": 85, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013515, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013587, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013624, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013719, "dur": 142, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013863, "dur": 90, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586013956, "dur": 96, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014054, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014055, "dur": 76, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014134, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014169, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014244, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014297, "dur": 14, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014313, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014350, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014423, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014526, "dur": 439, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014981, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586014985, "dur": 148, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586015136, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586015139, "dur": 341, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586015483, "dur": 265, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586015750, "dur": 29811, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586045567, "dur": 18, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586045586, "dur": 2613, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586048202, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586048205, "dur": 98489, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586146699, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586146704, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586146742, "dur": 14, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586146757, "dur": 42, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586146801, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586146842, "dur": 26, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586146869, "dur": 14, "ph": "X", "name": "ProcessMessages 5377", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586146883, "dur": 2727, "ph": "X", "name": "ReadAsync 5377", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586149612, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586149614, "dur": 542, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586150159, "dur": 33, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586150193, "dur": 122, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586150317, "dur": 205, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 66700, "tid": 12884901888, "ts": 1752353586150524, "dur": 2957, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 66700, "tid": 2487, "ts": 1752353586165022, "dur": 1413, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 66700, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 66700, "tid": 8589934592, "ts": 1752353585743808, "dur": 116569, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 66700, "tid": 8589934592, "ts": 1752353585860380, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 66700, "tid": 8589934592, "ts": 1752353585860389, "dur": 4346, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 66700, "tid": 2487, "ts": 1752353586166437, "dur": 21, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 66700, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 66700, "tid": 4294967296, "ts": 1752353585703714, "dur": 450973, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 66700, "tid": 4294967296, "ts": 1752353585707374, "dur": 28362, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 66700, "tid": 4294967296, "ts": 1752353586154800, "dur": 4163, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 66700, "tid": 4294967296, "ts": 1752353586156506, "dur": 1416, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 66700, "tid": 4294967296, "ts": 1752353586159025, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 66700, "tid": 2487, "ts": 1752353586166460, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752353585745810, "dur": 2795, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752353585748610, "dur": 20114, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752353585768786, "dur": 50, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752353585768836, "dur": 108, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752353585769638, "dur": 413, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_6DF747A371F03CBE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752353585770705, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45806E81BE62D86B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752353585770787, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3D9BC3FD62E6039E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752353585772138, "dur": 1836, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752353585775446, "dur": 1915, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752353585794832, "dur": 21114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752353585768951, "dur": 70973, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752353585839937, "dur": 310733, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752353586150837, "dur": 453, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752353585768900, "dur": 71042, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585839962, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752353585840154, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_E6099293577F65EB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585840365, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585840454, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_FDF1DF7383BE0CAD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585840577, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585840641, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_46E58BD6F1D896EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585840794, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585840882, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_97266B4C96017FCE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585841091, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585841245, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FD99BC8C7952C75B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585841444, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585841529, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_AEBC23A8E4FA79A7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585841695, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585841780, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_CA0E1C7BC0C607E9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585841929, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9179AE1968B95645.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585842009, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585842104, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_B15AC01DD6F6A05B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585842213, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585842326, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_65DFED6065744ECD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585842469, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585842576, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_A3EA01C4850945EE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585842706, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585842874, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_0897B8A47BC70881.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585842989, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585843199, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_60DE2D62F663D764.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585843359, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585843499, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_293131879DA5DD14.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585843613, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752353585844365, "dur": 2135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585846529, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585846586, "dur": 3327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585849914, "dur": 714, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585850775, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585851818, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585852305, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585852366, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585852774, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585852831, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585853306, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585853362, "dur": 2593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585855959, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585856060, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752353585856965, "dur": 2750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585859716, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585860025, "dur": 4431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1752353585864510, "dur": 3832, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585970967, "dur": 514, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585868585, "dur": 102906, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1752353585972602, "dur": 2149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585974751, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585974838, "dur": 4040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585978879, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585978954, "dur": 3782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585982737, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585982817, "dur": 3294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585986111, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585986197, "dur": 3642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585989839, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585989915, "dur": 3108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585993024, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585993140, "dur": 6153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353585999294, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353585999412, "dur": 3311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353586002723, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586002836, "dur": 4028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353586006865, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586007031, "dur": 3199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752353586010231, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586010393, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586010494, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586010562, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586010823, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586010914, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586011064, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586011139, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586011234, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/LitMotion.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752353586011285, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586011397, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586011454, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752353586011507, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586011608, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752353586011662, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586011779, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586011867, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752353586011946, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586012149, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586012264, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586012374, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/R3.Unity.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752353586012424, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586012566, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586012679, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586012788, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586012964, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752353586013024, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586013093, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752353586013147, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586013217, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586013278, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752353586013332, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586013520, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586013668, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586013761, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752353586013812, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586013916, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586014012, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586014151, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586014347, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586014482, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586014538, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752353586014589, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586014664, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752353586014770, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586014900, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586015019, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586015074, "dur": 901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752353586015995, "dur": 134722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585768903, "dur": 71061, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585839965, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752353585840232, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_9FEDC77DF18D321C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585840353, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_9EFE93A4B6B28B73.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585840461, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585840538, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_7D5EE36C09E1C813.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585840651, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585840780, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FAD28CEFAD4EF7CD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585840918, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585841069, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C9234425C371F41A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585841203, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585841342, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_061233166775A3D8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585841522, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585841663, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_6DF747A371F03CBE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585841774, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585841873, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_12CDA413F762C33F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585841993, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585842094, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_36001A2C42EABFDD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585842236, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585842369, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C59F701EDC7B3906.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585842503, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585842600, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A7772F47C16CAC56.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585842724, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_BA115901BA18BB2D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585842913, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585843018, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_A79420259383E98A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585843211, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585843282, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45806E81BE62D86B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585843490, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585843591, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585843914, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585843976, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585844288, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752353585844602, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585844809, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585845369, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585845430, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752353585845806, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585845927, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752353585846444, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585847082, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585847506, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585847598, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585847794, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585847947, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585848086, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585848593, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585849198, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585849323, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585849422, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585850002, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752353585850140, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585850302, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585850401, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585850555, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585850610, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585850822, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585850924, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585851077, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585851187, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UniTask.DOTween.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585851312, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585851552, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585851672, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585851760, "dur": 1894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752353585853684, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585854163, "dur": 3892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585858055, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585858343, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585860537, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585860626, "dur": 1694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585862353, "dur": 5751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585868105, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585868323, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585869373, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585869691, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585870002, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585870067, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585870156, "dur": 1787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585871983, "dur": 5406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585877389, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585877512, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585877718, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585878194, "dur": 3776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585881970, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585882054, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585882780, "dur": 943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/R3.Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585883765, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/NuGetForUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585884023, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585884078, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585884152, "dur": 2106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585886258, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585886345, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585886414, "dur": 3337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585889752, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585889876, "dur": 1409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/R3.Unity.XRInteractionToolkit.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585891315, "dur": 3166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752353585894506, "dur": 5008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585899514, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585899642, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585900468, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585901409, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585901993, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585902579, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585903307, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585904080, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585904714, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585905256, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585906027, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585906698, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585907259, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585908026, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585908641, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585909159, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585909699, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585910291, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585910956, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585911612, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585912279, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585912806, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585913266, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585913881, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585914465, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585914524, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585914766, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585915051, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585915114, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585915574, "dur": 73, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585915648, "dur": 56997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585972649, "dur": 4230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585976880, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585976958, "dur": 3467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585980425, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585980563, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585983134, "dur": 3474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585986608, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585986676, "dur": 3403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585990081, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353585990331, "dur": 4757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585995154, "dur": 4794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752353585999948, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353586000048, "dur": 5878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752353586005926, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353586005997, "dur": 9324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752353586015460, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752353586016263, "dur": 134443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585768910, "dur": 71065, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585839977, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_A93D6DF8E051997E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585840318, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_C4615A0A510AFA65.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585840423, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585840507, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_8B7F52F4EF803EEA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585840629, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585840711, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_DB3537FAE77D4E90.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585840828, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585840983, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8192A56F15586443.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585841174, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585841295, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E00800116238B81E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585841479, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585841556, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_2D77F886ADC51F6A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585841908, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585841999, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_06E5AC6350BD5612.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585842111, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585842204, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_FF3C62C209E1F2AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585842359, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585842479, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_EE513D35A608BF93.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585842672, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585842746, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_875874FE279274C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585842920, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585843009, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D0B6A15278A42C4B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585843209, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585843352, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_479F21D64DDF5A5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585843530, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585843705, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585843862, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585844265, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752353585844651, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585845296, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585845517, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585846360, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585847108, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752353585847601, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585847812, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585847959, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585848057, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585848251, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752353585848681, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585849262, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585849393, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585849507, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585849756, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585849937, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752353585850171, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585850254, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585850354, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585850465, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585850620, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585850835, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585850971, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585851046, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585851115, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585851240, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585851360, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585851478, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585851537, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585851644, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585851727, "dur": 1933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UniTask.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752353585853663, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585853730, "dur": 1737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585855484, "dur": 3722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585859206, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585859317, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_1DF7BBC55D029A75.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585859447, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585860278, "dur": 3823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585864101, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585864232, "dur": 4040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585868289, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585869281, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585869390, "dur": 2805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585872195, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585872262, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_3F93312DF16ABE8A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585872339, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585872412, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/LitMotion.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585872797, "dur": 1563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585874377, "dur": 1428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585875840, "dur": 2176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585878016, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585878081, "dur": 2921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585881003, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585881101, "dur": 2587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585883688, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585883790, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/LitMotion.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585884108, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585885019, "dur": 4495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585889514, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585889635, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585889706, "dur": 3263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585892969, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585893032, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585893952, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585894042, "dur": 4360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585898402, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585898723, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585898873, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585899249, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585900006, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585900904, "dur": 1944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585902888, "dur": 4406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585907295, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585907463, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585907542, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585908264, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585909666, "dur": 3951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585913617, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585913708, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_3971D385965D884C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752353585913791, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585914381, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585914846, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585915556, "dur": 73, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585915651, "dur": 56979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585972635, "dur": 3366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585976001, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585976121, "dur": 2565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585978686, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585978764, "dur": 4770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585983534, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585983611, "dur": 3581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/LitMotion.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585987193, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585987297, "dur": 2802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585990099, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585990372, "dur": 3358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585993730, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585993829, "dur": 4677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353585998506, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353585998722, "dur": 4194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353586002917, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586003087, "dur": 5341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353586008429, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586008534, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752353586010866, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586010984, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586011148, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586011268, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586011407, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586011498, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586011582, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752353586011665, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586011830, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586011918, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752353586012041, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752353586012121, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586012259, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752353586012319, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586012432, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752353586012484, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586012585, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586012686, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586012776, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752353586012875, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752353586012925, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586013053, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752353586013105, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586013235, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586013434, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586013573, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586013670, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586013842, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586013956, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/LitMotion.Extensions.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752353586014006, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586014140, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586014272, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586014372, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586014493, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586014609, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586014777, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586015031, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752353586015608, "dur": 135056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585768916, "dur": 71082, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585840004, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_5651B02FF685D981.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585840319, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585840404, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_5BD5A8CCC51B35E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585840517, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585840601, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_FBD6848B40F9D84E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585840714, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585840805, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_39B25D32CFFE745B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585840979, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585841096, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_4475AF2AFFABA926.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585841275, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585841392, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_58154A82C8AEEED2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585841540, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585841656, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2598F284265933B9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585841867, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585841941, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AEA8660476D6039F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585842011, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585842097, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_462079EBF000A463.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585842206, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585842267, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_15FE89EB11B2B491.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585842414, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585842498, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_5A158695308CA418.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585842659, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585842794, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0E99BC6E1964CACE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585842963, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585843141, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6799CBAE2582DA9C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585843256, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8D21704F39F1057D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585843492, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585843775, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_74A9378003EBEF66.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585843921, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_395D75F92AC91BB0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585844034, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585844115, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585844884, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585845309, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752353585845880, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585845991, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752353585846273, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585846389, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585846905, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585847524, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585847617, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585847819, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585847968, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585848095, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585848722, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585848808, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585848886, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752353585849154, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585849235, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585849376, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585849484, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585849867, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752353585850030, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/LitMotion.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585850121, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585850177, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585850293, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585850382, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585850518, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585850845, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585850901, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585851036, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585851092, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585851205, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585851316, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585851429, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585851523, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585851587, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585851692, "dur": 2320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752353585854045, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585856306, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585857356, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585857439, "dur": 4587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585862027, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585862148, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585863223, "dur": 4836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585868059, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585868211, "dur": 5266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585873477, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585873585, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585873641, "dur": 1365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585875049, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585876504, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585877570, "dur": 3651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585881221, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585881328, "dur": 3001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585884329, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585884444, "dur": 1521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585885996, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585887026, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585887146, "dur": 5573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/NuGetForUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585892720, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585892806, "dur": 2993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585895800, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585895991, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585896776, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585897054, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585897184, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585897505, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585897646, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585897950, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585898106, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585898823, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585899040, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585899590, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585900453, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585901396, "dur": 1754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/LitMotion.Extensions.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585903188, "dur": 4156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/LitMotion.Extensions.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585907344, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585907513, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585908246, "dur": 1598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585909873, "dur": 5205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585915080, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585915196, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752353585915279, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585915967, "dur": 56671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585972642, "dur": 4010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/R3.Unity.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585976653, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585976734, "dur": 2642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585979430, "dur": 2622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.Linq.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585982053, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585982181, "dur": 3310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585985491, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585985573, "dur": 2916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/R3.Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585988489, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585988577, "dur": 4238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/LitMotion.Extensions.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585992816, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585992926, "dur": 3137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585996121, "dur": 2964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353585999085, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353585999265, "dur": 3276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353586002541, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586002718, "dur": 8727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752353586011446, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586011644, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586011736, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586011848, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586011990, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586012133, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586012332, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/R3.Unity.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752353586012383, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586012485, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586012570, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586012705, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586012905, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586013038, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586013210, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586013317, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586013438, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586013569, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752353586013637, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586013700, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752353586013752, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586013891, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586014078, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752353586014160, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586014295, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586014449, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586014674, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586014932, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586015012, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586015472, "dur": 134590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752353586150109, "dur": 501, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752353586150613, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585768923, "dur": 71091, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585840021, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_C5C1DFA646678D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585840316, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585840406, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_C5F3E4FEF96D893F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585840520, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585840595, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_F0C4CE25855A5940.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585840719, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585840817, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_79266F7FBDBF49B4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585841021, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585841151, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_B519677B2E750245.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585841300, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585841426, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_1D0F8F342DF5CF0F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585841605, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585841701, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_0DC0DA8529CF1082.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585841858, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C30E9AD099929D47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585842028, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A07FB6BEB1E7333E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585842143, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585842254, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8D8F81C2AD007FF4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585842421, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585842506, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_DBE3A542E9C167DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585842659, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585842713, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_C1F8E474D4FC94AE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585842907, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585842973, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8F6AF5EA65F36C03.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585843188, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585843322, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3D9BC3FD62E6039E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585843486, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585843558, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585843715, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585844294, "dur": 1925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585846258, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585846345, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585846452, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585846762, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585847205, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585847438, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585847549, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585847644, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585847713, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585847861, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585847984, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585848079, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585848245, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752353585848443, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585848560, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585848678, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585849249, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585849375, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585849446, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585849555, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585850148, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585850339, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585850418, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585850599, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585850825, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585850986, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585851057, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585851170, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585851325, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585851516, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585851613, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585851686, "dur": 2134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/R3.Unity.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752353585853850, "dur": 1579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585855466, "dur": 2421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585857887, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585858054, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585858148, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585858292, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585858660, "dur": 4339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585862999, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585863055, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585863173, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585863387, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585863923, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585863976, "dur": 3789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585867800, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D33AAA747FCEED0A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585867920, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585868005, "dur": 7778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585875783, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585875991, "dur": 1914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585877922, "dur": 3709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585881631, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585881694, "dur": 4842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585886537, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585886634, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585886702, "dur": 3440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/LitMotion.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585890142, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585890247, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585890459, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585890559, "dur": 9956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585900516, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585900625, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_57B051C6BAF6DF9D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585900691, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353585900750, "dur": 2038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585902837, "dur": 13001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585915888, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585916028, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585916460, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585916526, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585916797, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752353585916934, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353585919310, "dur": 126723, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752353586046707, "dur": 1213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752353586048469, "dur": 189, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353586147104, "dur": 280, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752353586048954, "dur": 98438, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752353586150107, "dur": 516, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752353585768932, "dur": 71103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585840037, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A137065F34CC9C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585840312, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585840381, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_D7331C31AE624D95.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585840487, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585840567, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_E7D994A1187DB9B1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585840686, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585840791, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_CA2F35FCE4A30BF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585840949, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585841077, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_110B7A9752F5AD38.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585841239, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585841365, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_C268CA3985788B50.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585841539, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585841639, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_5A04CF1DC9DD1390.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585841850, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_070DA0E44072E4AB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585841999, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585842087, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_DB4D8D23A5102F12.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585842211, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585842318, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_ED9F64302AB7CA36.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585842501, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585842589, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E4ECE7E8BB35882.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585842695, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585842827, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_EB44D0B764B7C8DC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585842931, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585843086, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_06C0A05E69B9BD23.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585843244, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585843411, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_013EC6C429305933.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585843544, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752353585843866, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_03EB462F708254BF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585843994, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585844100, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752353585844425, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752353585844872, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752353585845406, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585845459, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752353585845789, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585845922, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752353585846349, "dur": 1153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752353585847532, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752353585847613, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585847841, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585847957, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585848102, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585848230, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752353585848299, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752353585848747, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585848831, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752353585849429, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585849693, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752353585850148, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585850372, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585850472, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752353585850824, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585850920, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585851073, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585851168, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585851345, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585851530, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585851637, "dur": 2201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752353585853841, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585853903, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585854676, "dur": 3983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585858659, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585858896, "dur": 2171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585861109, "dur": 5717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585866827, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585867356, "dur": 631, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1752353585867987, "dur": 3069, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1752353585871056, "dur": 641, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1752353585866946, "dur": 4752, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585871698, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_8E9298DC6B12ED57.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585871831, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/R3.Unity.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585872473, "dur": 4925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/R3.Unity.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585877399, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585877486, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585878385, "dur": 2645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585881030, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585881164, "dur": 3686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585884850, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585884937, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585885242, "dur": 5015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585890257, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585890393, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585890521, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585890601, "dur": 1449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585892051, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585892128, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/R3.Unity.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585892308, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585892429, "dur": 2537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/R3.Unity.XRInteractionToolkit.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585894966, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895122, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895205, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895348, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895478, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895574, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895685, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895743, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895822, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895928, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585895994, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585896618, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585896742, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585897122, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585897514, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585897627, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585897840, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585898005, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585898224, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585899055, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585899796, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585900597, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585901499, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585902114, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585902676, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585903439, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585904192, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585904944, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585905482, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585906266, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585906896, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585907607, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585908315, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585908844, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585909346, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585909941, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585910531, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585911215, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585911848, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585912544, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585912804, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585913183, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585913703, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585914317, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585914577, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585914660, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585915092, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585915196, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752353585915294, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585915800, "dur": 56813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585972626, "dur": 3031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585975658, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585975980, "dur": 3910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585979891, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585979971, "dur": 3487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585983512, "dur": 3863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585987375, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585987443, "dur": 3358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585990801, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585990949, "dur": 4392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585995342, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585995429, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353585997678, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353585997837, "dur": 5286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353586003123, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353586003223, "dur": 1693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/NuGetForUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353586004916, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752353586005337, "dur": 10608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752353586015979, "dur": 134716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585768938, "dur": 71120, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585840061, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_661E9919313836F1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585840431, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585840517, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_A8B77A263A174862.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585840642, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585840739, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585840884, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585841053, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_161CFFFA16B8213C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585841213, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585841347, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_89BDB88F64678315.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585841494, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585841618, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_845B1BE9B09512AE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585841783, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585841881, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_34F8FBC3386DE99E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585841995, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585842057, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A49295611C727860.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585842200, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585842275, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_165FE63856186A10.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585842427, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585842513, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_9358C75F1190FC64.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585842677, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585842731, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_97D4D89498459919.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585842914, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585843037, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A8529918C73873A7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585843213, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585843301, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_54E2C669D74DB25A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585843494, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585843781, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_6C262D80C93BD4F0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585843903, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_BA6BF233C73D6014.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585844083, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_665A96337EB7495A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585844212, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585845036, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585845136, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585845388, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585845442, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752353585845853, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585845966, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585846305, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585846681, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585847210, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585847264, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585847779, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585847939, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585848041, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585848158, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585848698, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UniTask.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585848892, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752353585849120, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585849190, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585849308, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585849564, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585849630, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585850576, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585850777, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585850830, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585850954, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585851040, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585851100, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585851255, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585851392, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585851502, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585851557, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585851676, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585851732, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752353585853850, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585853923, "dur": 1648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585855612, "dur": 1750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585857362, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585857599, "dur": 2990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585860589, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585860733, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585860799, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585860886, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585861951, "dur": 9437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585871388, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585871548, "dur": 4650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585876199, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585876294, "dur": 5437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/LitMotion.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585881731, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585881860, "dur": 3400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585885260, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585885354, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585885878, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585887390, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585887620, "dur": 3665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/R3.Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585891286, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585891450, "dur": 1368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585892839, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585893465, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585893593, "dur": 4562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/R3.Unity.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585898156, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585898239, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585899061, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585899799, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585900604, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585901506, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585902123, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585902666, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585903455, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585903970, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585904076, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585904618, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585905198, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585905925, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585906547, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585907076, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585907831, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585908485, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585909021, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585909534, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585910120, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585910778, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585911424, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585912076, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585912760, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585912876, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585913360, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585913979, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585914343, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585914464, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585915090, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585915507, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585915647, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585916476, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752353585916584, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585916893, "dur": 55760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585972659, "dur": 2084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585974744, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585974876, "dur": 3864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585978741, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585978824, "dur": 2924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585981784, "dur": 2704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585984489, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585984569, "dur": 3409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.DOTween.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585987978, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585988075, "dur": 3846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585991922, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585992069, "dur": 2776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585994846, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585995060, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585995149, "dur": 4652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353585999802, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353585999954, "dur": 3862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353586003817, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353586003925, "dur": 3575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/R3.Unity.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353586007501, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752353586007559, "dur": 8018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752353586015601, "dur": 135119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585768945, "dur": 71136, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585840092, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5CDB3D0CA3CB09B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585840364, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0254B4EAD9FA5CB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585840611, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585840713, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2A0E14B20F8987CC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585840832, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585840991, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_355EDFCE7F3816F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585841159, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585841272, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0D7C2BFD403ED278.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585841449, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585841516, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_6C9DA28BBAE3FB82.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585841672, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585841771, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D7B1BF987A36C198.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585841902, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585841989, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0462768E64EABD0A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585842130, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585842219, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7436A235A0073526.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585842435, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A6AABF0E6ADA0B25.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585842582, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585842681, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_E5822FB88B5F8525.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585842831, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585842925, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_4782BF78D66FB2E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585843089, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585843216, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4574CE91C336EF7D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585843358, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585843488, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5A2D3B4655B3286F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585843598, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585843802, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_8C284CAFF6588524.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585843857, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_857F5E2A119BA0C9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585843947, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585844025, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_ED2AB5385077AD72.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585844087, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585844144, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585844201, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752353585844503, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752353585844848, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752353585845349, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585845408, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752353585845721, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752353585845881, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585846044, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752353585846100, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752353585846687, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752353585846852, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752353585847250, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752353585847512, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752353585847575, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585847701, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585847889, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585848012, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585848120, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585848193, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752353585848763, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752353585849099, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585849227, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585849437, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585849531, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752353585850141, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585850230, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585850350, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585850446, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752353585850596, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752353585850808, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585850883, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585851011, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585851065, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585851165, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585851340, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585851454, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585851511, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585851593, "dur": 1970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/R3.Unity.XRInteractionToolkit.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752353585853579, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585853880, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585853945, "dur": 17696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585871641, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585871785, "dur": 1019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585872816, "dur": 10904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585883720, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585883815, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585883874, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585883975, "dur": 5980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585889956, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585890068, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_059BC5464FF14C2B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585890197, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585890620, "dur": 10323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585900944, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585901034, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_C4A720C3D568CE76.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585901183, "dur": 1490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585902706, "dur": 5149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585907856, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585907965, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Runtime.ref.dll_DEAB70CB54ED1324.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585908066, "dur": 1440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585909533, "dur": 5043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585914576, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585914681, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752353585914871, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585915508, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585915648, "dur": 57015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585972663, "dur": 4290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585976953, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585977033, "dur": 2625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585979706, "dur": 3408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/R3.Unity.XRInteractionToolkit.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585983115, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585983276, "dur": 3859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585987188, "dur": 3254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585990443, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585990547, "dur": 4599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585995147, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585995282, "dur": 2169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353585997451, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353585997687, "dur": 3773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353586001460, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353586001550, "dur": 5324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353586006875, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353586006997, "dur": 2775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353586009773, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752353586009850, "dur": 6389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/LitMotion.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752353586016259, "dur": 134396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752353586152799, "dur": 988, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 66700, "tid": 2487, "ts": 1752353586166952, "dur": 5799, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 66700, "tid": 2487, "ts": 1752353586172792, "dur": 1564, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 66700, "tid": 2487, "ts": 1752353586162887, "dur": 12150, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}