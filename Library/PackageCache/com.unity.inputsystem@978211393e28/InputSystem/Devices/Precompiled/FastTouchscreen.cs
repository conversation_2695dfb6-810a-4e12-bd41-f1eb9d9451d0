//------------------------------------------------------------------------------
// <auto-generated>
//     This code was auto-generated by com.unity.inputsystem:InputLayoutCodeGenerator
//     version 1.14.1
//     from "Touchscreen" layout
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using UnityEngine.InputSystem;
using UnityEngine.InputSystem.LowLevel;
using UnityEngine.InputSystem.Utilities;

// Suppress warnings from local variables for control references
// that we don't end up using.
#pragma warning disable CS0219

namespace UnityEngine.InputSystem
{
    internal partial class FastTouchscreen : UnityEngine.InputSystem.Touchscreen
    {
        public const string metadata = "AutoWindowSpace;Touch;Vector2;Delta;Analog;TouchPress;Button;Axis;Integer;TouchPhase;Double;Touchscreen;Pointer";
        public FastTouchscreen()
        {
            var builder = this.Setup(302, 5, 0)
                .WithName("Touchscreen")
                .WithDisplayName("Touchscreen")
                .WithChildren(0, 17)
                .WithLayout(new InternedString("Touchscreen"))
                .WithStateBlock(new InputStateBlock { format = new FourCC(1414742866), sizeInBits = 4928 });

            var kTouchLayout = new InternedString("Touch");
            var kVector2Layout = new InternedString("Vector2");
            var kDeltaLayout = new InternedString("Delta");
            var kAnalogLayout = new InternedString("Analog");
            var kTouchPressLayout = new InternedString("TouchPress");
            var kIntegerLayout = new InternedString("Integer");
            var kAxisLayout = new InternedString("Axis");
            var kTouchPhaseLayout = new InternedString("TouchPhase");
            var kButtonLayout = new InternedString("Button");
            var kDoubleLayout = new InternedString("Double");

            // /Touchscreen/primaryTouch
            var ctrlTouchscreenprimaryTouch = Initialize_ctrlTouchscreenprimaryTouch(kTouchLayout, this);

            // /Touchscreen/position
            var ctrlTouchscreenposition = Initialize_ctrlTouchscreenposition(kVector2Layout, this);

            // /Touchscreen/delta
            var ctrlTouchscreendelta = Initialize_ctrlTouchscreendelta(kDeltaLayout, this);

            // /Touchscreen/pressure
            var ctrlTouchscreenpressure = Initialize_ctrlTouchscreenpressure(kAnalogLayout, this);

            // /Touchscreen/radius
            var ctrlTouchscreenradius = Initialize_ctrlTouchscreenradius(kVector2Layout, this);

            // /Touchscreen/press
            var ctrlTouchscreenpress = Initialize_ctrlTouchscreenpress(kTouchPressLayout, this);

            // /Touchscreen/displayIndex
            var ctrlTouchscreendisplayIndex = Initialize_ctrlTouchscreendisplayIndex(kIntegerLayout, this);

            // /Touchscreen/touch0
            var ctrlTouchscreentouch0 = Initialize_ctrlTouchscreentouch0(kTouchLayout, this);

            // /Touchscreen/touch1
            var ctrlTouchscreentouch1 = Initialize_ctrlTouchscreentouch1(kTouchLayout, this);

            // /Touchscreen/touch2
            var ctrlTouchscreentouch2 = Initialize_ctrlTouchscreentouch2(kTouchLayout, this);

            // /Touchscreen/touch3
            var ctrlTouchscreentouch3 = Initialize_ctrlTouchscreentouch3(kTouchLayout, this);

            // /Touchscreen/touch4
            var ctrlTouchscreentouch4 = Initialize_ctrlTouchscreentouch4(kTouchLayout, this);

            // /Touchscreen/touch5
            var ctrlTouchscreentouch5 = Initialize_ctrlTouchscreentouch5(kTouchLayout, this);

            // /Touchscreen/touch6
            var ctrlTouchscreentouch6 = Initialize_ctrlTouchscreentouch6(kTouchLayout, this);

            // /Touchscreen/touch7
            var ctrlTouchscreentouch7 = Initialize_ctrlTouchscreentouch7(kTouchLayout, this);

            // /Touchscreen/touch8
            var ctrlTouchscreentouch8 = Initialize_ctrlTouchscreentouch8(kTouchLayout, this);

            // /Touchscreen/touch9
            var ctrlTouchscreentouch9 = Initialize_ctrlTouchscreentouch9(kTouchLayout, this);

            // /Touchscreen/primaryTouch/touchId
            var ctrlTouchscreenprimaryTouchtouchId = Initialize_ctrlTouchscreenprimaryTouchtouchId(kIntegerLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/position
            var ctrlTouchscreenprimaryTouchposition = Initialize_ctrlTouchscreenprimaryTouchposition(kVector2Layout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/delta
            var ctrlTouchscreenprimaryTouchdelta = Initialize_ctrlTouchscreenprimaryTouchdelta(kDeltaLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/pressure
            var ctrlTouchscreenprimaryTouchpressure = Initialize_ctrlTouchscreenprimaryTouchpressure(kAxisLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/radius
            var ctrlTouchscreenprimaryTouchradius = Initialize_ctrlTouchscreenprimaryTouchradius(kVector2Layout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/phase
            var ctrlTouchscreenprimaryTouchphase = Initialize_ctrlTouchscreenprimaryTouchphase(kTouchPhaseLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/press
            var ctrlTouchscreenprimaryTouchpress = Initialize_ctrlTouchscreenprimaryTouchpress(kTouchPressLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/tapCount
            var ctrlTouchscreenprimaryTouchtapCount = Initialize_ctrlTouchscreenprimaryTouchtapCount(kIntegerLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/displayIndex
            var ctrlTouchscreenprimaryTouchdisplayIndex = Initialize_ctrlTouchscreenprimaryTouchdisplayIndex(kIntegerLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/indirectTouch
            var ctrlTouchscreenprimaryTouchindirectTouch = Initialize_ctrlTouchscreenprimaryTouchindirectTouch(kButtonLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/tap
            var ctrlTouchscreenprimaryTouchtap = Initialize_ctrlTouchscreenprimaryTouchtap(kButtonLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/startTime
            var ctrlTouchscreenprimaryTouchstartTime = Initialize_ctrlTouchscreenprimaryTouchstartTime(kDoubleLayout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/startPosition
            var ctrlTouchscreenprimaryTouchstartPosition = Initialize_ctrlTouchscreenprimaryTouchstartPosition(kVector2Layout, ctrlTouchscreenprimaryTouch);

            // /Touchscreen/primaryTouch/position/x
            var ctrlTouchscreenprimaryTouchpositionx = Initialize_ctrlTouchscreenprimaryTouchpositionx(kAxisLayout, ctrlTouchscreenprimaryTouchposition);

            // /Touchscreen/primaryTouch/position/y
            var ctrlTouchscreenprimaryTouchpositiony = Initialize_ctrlTouchscreenprimaryTouchpositiony(kAxisLayout, ctrlTouchscreenprimaryTouchposition);

            // /Touchscreen/primaryTouch/delta/up
            var ctrlTouchscreenprimaryTouchdeltaup = Initialize_ctrlTouchscreenprimaryTouchdeltaup(kAxisLayout, ctrlTouchscreenprimaryTouchdelta);

            // /Touchscreen/primaryTouch/delta/down
            var ctrlTouchscreenprimaryTouchdeltadown = Initialize_ctrlTouchscreenprimaryTouchdeltadown(kAxisLayout, ctrlTouchscreenprimaryTouchdelta);

            // /Touchscreen/primaryTouch/delta/left
            var ctrlTouchscreenprimaryTouchdeltaleft = Initialize_ctrlTouchscreenprimaryTouchdeltaleft(kAxisLayout, ctrlTouchscreenprimaryTouchdelta);

            // /Touchscreen/primaryTouch/delta/right
            var ctrlTouchscreenprimaryTouchdeltaright = Initialize_ctrlTouchscreenprimaryTouchdeltaright(kAxisLayout, ctrlTouchscreenprimaryTouchdelta);

            // /Touchscreen/primaryTouch/delta/x
            var ctrlTouchscreenprimaryTouchdeltax = Initialize_ctrlTouchscreenprimaryTouchdeltax(kAxisLayout, ctrlTouchscreenprimaryTouchdelta);

            // /Touchscreen/primaryTouch/delta/y
            var ctrlTouchscreenprimaryTouchdeltay = Initialize_ctrlTouchscreenprimaryTouchdeltay(kAxisLayout, ctrlTouchscreenprimaryTouchdelta);

            // /Touchscreen/primaryTouch/radius/x
            var ctrlTouchscreenprimaryTouchradiusx = Initialize_ctrlTouchscreenprimaryTouchradiusx(kAxisLayout, ctrlTouchscreenprimaryTouchradius);

            // /Touchscreen/primaryTouch/radius/y
            var ctrlTouchscreenprimaryTouchradiusy = Initialize_ctrlTouchscreenprimaryTouchradiusy(kAxisLayout, ctrlTouchscreenprimaryTouchradius);

            // /Touchscreen/primaryTouch/startPosition/x
            var ctrlTouchscreenprimaryTouchstartPositionx = Initialize_ctrlTouchscreenprimaryTouchstartPositionx(kAxisLayout, ctrlTouchscreenprimaryTouchstartPosition);

            // /Touchscreen/primaryTouch/startPosition/y
            var ctrlTouchscreenprimaryTouchstartPositiony = Initialize_ctrlTouchscreenprimaryTouchstartPositiony(kAxisLayout, ctrlTouchscreenprimaryTouchstartPosition);

            // /Touchscreen/position/x
            var ctrlTouchscreenpositionx = Initialize_ctrlTouchscreenpositionx(kAxisLayout, ctrlTouchscreenposition);

            // /Touchscreen/position/y
            var ctrlTouchscreenpositiony = Initialize_ctrlTouchscreenpositiony(kAxisLayout, ctrlTouchscreenposition);

            // /Touchscreen/delta/up
            var ctrlTouchscreendeltaup = Initialize_ctrlTouchscreendeltaup(kAxisLayout, ctrlTouchscreendelta);

            // /Touchscreen/delta/down
            var ctrlTouchscreendeltadown = Initialize_ctrlTouchscreendeltadown(kAxisLayout, ctrlTouchscreendelta);

            // /Touchscreen/delta/left
            var ctrlTouchscreendeltaleft = Initialize_ctrlTouchscreendeltaleft(kAxisLayout, ctrlTouchscreendelta);

            // /Touchscreen/delta/right
            var ctrlTouchscreendeltaright = Initialize_ctrlTouchscreendeltaright(kAxisLayout, ctrlTouchscreendelta);

            // /Touchscreen/delta/x
            var ctrlTouchscreendeltax = Initialize_ctrlTouchscreendeltax(kAxisLayout, ctrlTouchscreendelta);

            // /Touchscreen/delta/y
            var ctrlTouchscreendeltay = Initialize_ctrlTouchscreendeltay(kAxisLayout, ctrlTouchscreendelta);

            // /Touchscreen/radius/x
            var ctrlTouchscreenradiusx = Initialize_ctrlTouchscreenradiusx(kAxisLayout, ctrlTouchscreenradius);

            // /Touchscreen/radius/y
            var ctrlTouchscreenradiusy = Initialize_ctrlTouchscreenradiusy(kAxisLayout, ctrlTouchscreenradius);

            // /Touchscreen/touch0/touchId
            var ctrlTouchscreentouch0touchId = Initialize_ctrlTouchscreentouch0touchId(kIntegerLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/position
            var ctrlTouchscreentouch0position = Initialize_ctrlTouchscreentouch0position(kVector2Layout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/delta
            var ctrlTouchscreentouch0delta = Initialize_ctrlTouchscreentouch0delta(kDeltaLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/pressure
            var ctrlTouchscreentouch0pressure = Initialize_ctrlTouchscreentouch0pressure(kAxisLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/radius
            var ctrlTouchscreentouch0radius = Initialize_ctrlTouchscreentouch0radius(kVector2Layout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/phase
            var ctrlTouchscreentouch0phase = Initialize_ctrlTouchscreentouch0phase(kTouchPhaseLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/press
            var ctrlTouchscreentouch0press = Initialize_ctrlTouchscreentouch0press(kTouchPressLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/tapCount
            var ctrlTouchscreentouch0tapCount = Initialize_ctrlTouchscreentouch0tapCount(kIntegerLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/displayIndex
            var ctrlTouchscreentouch0displayIndex = Initialize_ctrlTouchscreentouch0displayIndex(kIntegerLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/indirectTouch
            var ctrlTouchscreentouch0indirectTouch = Initialize_ctrlTouchscreentouch0indirectTouch(kButtonLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/tap
            var ctrlTouchscreentouch0tap = Initialize_ctrlTouchscreentouch0tap(kButtonLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/startTime
            var ctrlTouchscreentouch0startTime = Initialize_ctrlTouchscreentouch0startTime(kDoubleLayout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/startPosition
            var ctrlTouchscreentouch0startPosition = Initialize_ctrlTouchscreentouch0startPosition(kVector2Layout, ctrlTouchscreentouch0);

            // /Touchscreen/touch0/position/x
            var ctrlTouchscreentouch0positionx = Initialize_ctrlTouchscreentouch0positionx(kAxisLayout, ctrlTouchscreentouch0position);

            // /Touchscreen/touch0/position/y
            var ctrlTouchscreentouch0positiony = Initialize_ctrlTouchscreentouch0positiony(kAxisLayout, ctrlTouchscreentouch0position);

            // /Touchscreen/touch0/delta/up
            var ctrlTouchscreentouch0deltaup = Initialize_ctrlTouchscreentouch0deltaup(kAxisLayout, ctrlTouchscreentouch0delta);

            // /Touchscreen/touch0/delta/down
            var ctrlTouchscreentouch0deltadown = Initialize_ctrlTouchscreentouch0deltadown(kAxisLayout, ctrlTouchscreentouch0delta);

            // /Touchscreen/touch0/delta/left
            var ctrlTouchscreentouch0deltaleft = Initialize_ctrlTouchscreentouch0deltaleft(kAxisLayout, ctrlTouchscreentouch0delta);

            // /Touchscreen/touch0/delta/right
            var ctrlTouchscreentouch0deltaright = Initialize_ctrlTouchscreentouch0deltaright(kAxisLayout, ctrlTouchscreentouch0delta);

            // /Touchscreen/touch0/delta/x
            var ctrlTouchscreentouch0deltax = Initialize_ctrlTouchscreentouch0deltax(kAxisLayout, ctrlTouchscreentouch0delta);

            // /Touchscreen/touch0/delta/y
            var ctrlTouchscreentouch0deltay = Initialize_ctrlTouchscreentouch0deltay(kAxisLayout, ctrlTouchscreentouch0delta);

            // /Touchscreen/touch0/radius/x
            var ctrlTouchscreentouch0radiusx = Initialize_ctrlTouchscreentouch0radiusx(kAxisLayout, ctrlTouchscreentouch0radius);

            // /Touchscreen/touch0/radius/y
            var ctrlTouchscreentouch0radiusy = Initialize_ctrlTouchscreentouch0radiusy(kAxisLayout, ctrlTouchscreentouch0radius);

            // /Touchscreen/touch0/startPosition/x
            var ctrlTouchscreentouch0startPositionx = Initialize_ctrlTouchscreentouch0startPositionx(kAxisLayout, ctrlTouchscreentouch0startPosition);

            // /Touchscreen/touch0/startPosition/y
            var ctrlTouchscreentouch0startPositiony = Initialize_ctrlTouchscreentouch0startPositiony(kAxisLayout, ctrlTouchscreentouch0startPosition);

            // /Touchscreen/touch1/touchId
            var ctrlTouchscreentouch1touchId = Initialize_ctrlTouchscreentouch1touchId(kIntegerLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/position
            var ctrlTouchscreentouch1position = Initialize_ctrlTouchscreentouch1position(kVector2Layout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/delta
            var ctrlTouchscreentouch1delta = Initialize_ctrlTouchscreentouch1delta(kDeltaLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/pressure
            var ctrlTouchscreentouch1pressure = Initialize_ctrlTouchscreentouch1pressure(kAxisLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/radius
            var ctrlTouchscreentouch1radius = Initialize_ctrlTouchscreentouch1radius(kVector2Layout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/phase
            var ctrlTouchscreentouch1phase = Initialize_ctrlTouchscreentouch1phase(kTouchPhaseLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/press
            var ctrlTouchscreentouch1press = Initialize_ctrlTouchscreentouch1press(kTouchPressLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/tapCount
            var ctrlTouchscreentouch1tapCount = Initialize_ctrlTouchscreentouch1tapCount(kIntegerLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/displayIndex
            var ctrlTouchscreentouch1displayIndex = Initialize_ctrlTouchscreentouch1displayIndex(kIntegerLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/indirectTouch
            var ctrlTouchscreentouch1indirectTouch = Initialize_ctrlTouchscreentouch1indirectTouch(kButtonLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/tap
            var ctrlTouchscreentouch1tap = Initialize_ctrlTouchscreentouch1tap(kButtonLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/startTime
            var ctrlTouchscreentouch1startTime = Initialize_ctrlTouchscreentouch1startTime(kDoubleLayout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/startPosition
            var ctrlTouchscreentouch1startPosition = Initialize_ctrlTouchscreentouch1startPosition(kVector2Layout, ctrlTouchscreentouch1);

            // /Touchscreen/touch1/position/x
            var ctrlTouchscreentouch1positionx = Initialize_ctrlTouchscreentouch1positionx(kAxisLayout, ctrlTouchscreentouch1position);

            // /Touchscreen/touch1/position/y
            var ctrlTouchscreentouch1positiony = Initialize_ctrlTouchscreentouch1positiony(kAxisLayout, ctrlTouchscreentouch1position);

            // /Touchscreen/touch1/delta/up
            var ctrlTouchscreentouch1deltaup = Initialize_ctrlTouchscreentouch1deltaup(kAxisLayout, ctrlTouchscreentouch1delta);

            // /Touchscreen/touch1/delta/down
            var ctrlTouchscreentouch1deltadown = Initialize_ctrlTouchscreentouch1deltadown(kAxisLayout, ctrlTouchscreentouch1delta);

            // /Touchscreen/touch1/delta/left
            var ctrlTouchscreentouch1deltaleft = Initialize_ctrlTouchscreentouch1deltaleft(kAxisLayout, ctrlTouchscreentouch1delta);

            // /Touchscreen/touch1/delta/right
            var ctrlTouchscreentouch1deltaright = Initialize_ctrlTouchscreentouch1deltaright(kAxisLayout, ctrlTouchscreentouch1delta);

            // /Touchscreen/touch1/delta/x
            var ctrlTouchscreentouch1deltax = Initialize_ctrlTouchscreentouch1deltax(kAxisLayout, ctrlTouchscreentouch1delta);

            // /Touchscreen/touch1/delta/y
            var ctrlTouchscreentouch1deltay = Initialize_ctrlTouchscreentouch1deltay(kAxisLayout, ctrlTouchscreentouch1delta);

            // /Touchscreen/touch1/radius/x
            var ctrlTouchscreentouch1radiusx = Initialize_ctrlTouchscreentouch1radiusx(kAxisLayout, ctrlTouchscreentouch1radius);

            // /Touchscreen/touch1/radius/y
            var ctrlTouchscreentouch1radiusy = Initialize_ctrlTouchscreentouch1radiusy(kAxisLayout, ctrlTouchscreentouch1radius);

            // /Touchscreen/touch1/startPosition/x
            var ctrlTouchscreentouch1startPositionx = Initialize_ctrlTouchscreentouch1startPositionx(kAxisLayout, ctrlTouchscreentouch1startPosition);

            // /Touchscreen/touch1/startPosition/y
            var ctrlTouchscreentouch1startPositiony = Initialize_ctrlTouchscreentouch1startPositiony(kAxisLayout, ctrlTouchscreentouch1startPosition);

            // /Touchscreen/touch2/touchId
            var ctrlTouchscreentouch2touchId = Initialize_ctrlTouchscreentouch2touchId(kIntegerLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/position
            var ctrlTouchscreentouch2position = Initialize_ctrlTouchscreentouch2position(kVector2Layout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/delta
            var ctrlTouchscreentouch2delta = Initialize_ctrlTouchscreentouch2delta(kDeltaLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/pressure
            var ctrlTouchscreentouch2pressure = Initialize_ctrlTouchscreentouch2pressure(kAxisLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/radius
            var ctrlTouchscreentouch2radius = Initialize_ctrlTouchscreentouch2radius(kVector2Layout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/phase
            var ctrlTouchscreentouch2phase = Initialize_ctrlTouchscreentouch2phase(kTouchPhaseLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/press
            var ctrlTouchscreentouch2press = Initialize_ctrlTouchscreentouch2press(kTouchPressLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/tapCount
            var ctrlTouchscreentouch2tapCount = Initialize_ctrlTouchscreentouch2tapCount(kIntegerLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/displayIndex
            var ctrlTouchscreentouch2displayIndex = Initialize_ctrlTouchscreentouch2displayIndex(kIntegerLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/indirectTouch
            var ctrlTouchscreentouch2indirectTouch = Initialize_ctrlTouchscreentouch2indirectTouch(kButtonLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/tap
            var ctrlTouchscreentouch2tap = Initialize_ctrlTouchscreentouch2tap(kButtonLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/startTime
            var ctrlTouchscreentouch2startTime = Initialize_ctrlTouchscreentouch2startTime(kDoubleLayout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/startPosition
            var ctrlTouchscreentouch2startPosition = Initialize_ctrlTouchscreentouch2startPosition(kVector2Layout, ctrlTouchscreentouch2);

            // /Touchscreen/touch2/position/x
            var ctrlTouchscreentouch2positionx = Initialize_ctrlTouchscreentouch2positionx(kAxisLayout, ctrlTouchscreentouch2position);

            // /Touchscreen/touch2/position/y
            var ctrlTouchscreentouch2positiony = Initialize_ctrlTouchscreentouch2positiony(kAxisLayout, ctrlTouchscreentouch2position);

            // /Touchscreen/touch2/delta/up
            var ctrlTouchscreentouch2deltaup = Initialize_ctrlTouchscreentouch2deltaup(kAxisLayout, ctrlTouchscreentouch2delta);

            // /Touchscreen/touch2/delta/down
            var ctrlTouchscreentouch2deltadown = Initialize_ctrlTouchscreentouch2deltadown(kAxisLayout, ctrlTouchscreentouch2delta);

            // /Touchscreen/touch2/delta/left
            var ctrlTouchscreentouch2deltaleft = Initialize_ctrlTouchscreentouch2deltaleft(kAxisLayout, ctrlTouchscreentouch2delta);

            // /Touchscreen/touch2/delta/right
            var ctrlTouchscreentouch2deltaright = Initialize_ctrlTouchscreentouch2deltaright(kAxisLayout, ctrlTouchscreentouch2delta);

            // /Touchscreen/touch2/delta/x
            var ctrlTouchscreentouch2deltax = Initialize_ctrlTouchscreentouch2deltax(kAxisLayout, ctrlTouchscreentouch2delta);

            // /Touchscreen/touch2/delta/y
            var ctrlTouchscreentouch2deltay = Initialize_ctrlTouchscreentouch2deltay(kAxisLayout, ctrlTouchscreentouch2delta);

            // /Touchscreen/touch2/radius/x
            var ctrlTouchscreentouch2radiusx = Initialize_ctrlTouchscreentouch2radiusx(kAxisLayout, ctrlTouchscreentouch2radius);

            // /Touchscreen/touch2/radius/y
            var ctrlTouchscreentouch2radiusy = Initialize_ctrlTouchscreentouch2radiusy(kAxisLayout, ctrlTouchscreentouch2radius);

            // /Touchscreen/touch2/startPosition/x
            var ctrlTouchscreentouch2startPositionx = Initialize_ctrlTouchscreentouch2startPositionx(kAxisLayout, ctrlTouchscreentouch2startPosition);

            // /Touchscreen/touch2/startPosition/y
            var ctrlTouchscreentouch2startPositiony = Initialize_ctrlTouchscreentouch2startPositiony(kAxisLayout, ctrlTouchscreentouch2startPosition);

            // /Touchscreen/touch3/touchId
            var ctrlTouchscreentouch3touchId = Initialize_ctrlTouchscreentouch3touchId(kIntegerLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/position
            var ctrlTouchscreentouch3position = Initialize_ctrlTouchscreentouch3position(kVector2Layout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/delta
            var ctrlTouchscreentouch3delta = Initialize_ctrlTouchscreentouch3delta(kDeltaLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/pressure
            var ctrlTouchscreentouch3pressure = Initialize_ctrlTouchscreentouch3pressure(kAxisLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/radius
            var ctrlTouchscreentouch3radius = Initialize_ctrlTouchscreentouch3radius(kVector2Layout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/phase
            var ctrlTouchscreentouch3phase = Initialize_ctrlTouchscreentouch3phase(kTouchPhaseLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/press
            var ctrlTouchscreentouch3press = Initialize_ctrlTouchscreentouch3press(kTouchPressLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/tapCount
            var ctrlTouchscreentouch3tapCount = Initialize_ctrlTouchscreentouch3tapCount(kIntegerLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/displayIndex
            var ctrlTouchscreentouch3displayIndex = Initialize_ctrlTouchscreentouch3displayIndex(kIntegerLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/indirectTouch
            var ctrlTouchscreentouch3indirectTouch = Initialize_ctrlTouchscreentouch3indirectTouch(kButtonLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/tap
            var ctrlTouchscreentouch3tap = Initialize_ctrlTouchscreentouch3tap(kButtonLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/startTime
            var ctrlTouchscreentouch3startTime = Initialize_ctrlTouchscreentouch3startTime(kDoubleLayout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/startPosition
            var ctrlTouchscreentouch3startPosition = Initialize_ctrlTouchscreentouch3startPosition(kVector2Layout, ctrlTouchscreentouch3);

            // /Touchscreen/touch3/position/x
            var ctrlTouchscreentouch3positionx = Initialize_ctrlTouchscreentouch3positionx(kAxisLayout, ctrlTouchscreentouch3position);

            // /Touchscreen/touch3/position/y
            var ctrlTouchscreentouch3positiony = Initialize_ctrlTouchscreentouch3positiony(kAxisLayout, ctrlTouchscreentouch3position);

            // /Touchscreen/touch3/delta/up
            var ctrlTouchscreentouch3deltaup = Initialize_ctrlTouchscreentouch3deltaup(kAxisLayout, ctrlTouchscreentouch3delta);

            // /Touchscreen/touch3/delta/down
            var ctrlTouchscreentouch3deltadown = Initialize_ctrlTouchscreentouch3deltadown(kAxisLayout, ctrlTouchscreentouch3delta);

            // /Touchscreen/touch3/delta/left
            var ctrlTouchscreentouch3deltaleft = Initialize_ctrlTouchscreentouch3deltaleft(kAxisLayout, ctrlTouchscreentouch3delta);

            // /Touchscreen/touch3/delta/right
            var ctrlTouchscreentouch3deltaright = Initialize_ctrlTouchscreentouch3deltaright(kAxisLayout, ctrlTouchscreentouch3delta);

            // /Touchscreen/touch3/delta/x
            var ctrlTouchscreentouch3deltax = Initialize_ctrlTouchscreentouch3deltax(kAxisLayout, ctrlTouchscreentouch3delta);

            // /Touchscreen/touch3/delta/y
            var ctrlTouchscreentouch3deltay = Initialize_ctrlTouchscreentouch3deltay(kAxisLayout, ctrlTouchscreentouch3delta);

            // /Touchscreen/touch3/radius/x
            var ctrlTouchscreentouch3radiusx = Initialize_ctrlTouchscreentouch3radiusx(kAxisLayout, ctrlTouchscreentouch3radius);

            // /Touchscreen/touch3/radius/y
            var ctrlTouchscreentouch3radiusy = Initialize_ctrlTouchscreentouch3radiusy(kAxisLayout, ctrlTouchscreentouch3radius);

            // /Touchscreen/touch3/startPosition/x
            var ctrlTouchscreentouch3startPositionx = Initialize_ctrlTouchscreentouch3startPositionx(kAxisLayout, ctrlTouchscreentouch3startPosition);

            // /Touchscreen/touch3/startPosition/y
            var ctrlTouchscreentouch3startPositiony = Initialize_ctrlTouchscreentouch3startPositiony(kAxisLayout, ctrlTouchscreentouch3startPosition);

            // /Touchscreen/touch4/touchId
            var ctrlTouchscreentouch4touchId = Initialize_ctrlTouchscreentouch4touchId(kIntegerLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/position
            var ctrlTouchscreentouch4position = Initialize_ctrlTouchscreentouch4position(kVector2Layout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/delta
            var ctrlTouchscreentouch4delta = Initialize_ctrlTouchscreentouch4delta(kDeltaLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/pressure
            var ctrlTouchscreentouch4pressure = Initialize_ctrlTouchscreentouch4pressure(kAxisLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/radius
            var ctrlTouchscreentouch4radius = Initialize_ctrlTouchscreentouch4radius(kVector2Layout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/phase
            var ctrlTouchscreentouch4phase = Initialize_ctrlTouchscreentouch4phase(kTouchPhaseLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/press
            var ctrlTouchscreentouch4press = Initialize_ctrlTouchscreentouch4press(kTouchPressLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/tapCount
            var ctrlTouchscreentouch4tapCount = Initialize_ctrlTouchscreentouch4tapCount(kIntegerLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/displayIndex
            var ctrlTouchscreentouch4displayIndex = Initialize_ctrlTouchscreentouch4displayIndex(kIntegerLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/indirectTouch
            var ctrlTouchscreentouch4indirectTouch = Initialize_ctrlTouchscreentouch4indirectTouch(kButtonLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/tap
            var ctrlTouchscreentouch4tap = Initialize_ctrlTouchscreentouch4tap(kButtonLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/startTime
            var ctrlTouchscreentouch4startTime = Initialize_ctrlTouchscreentouch4startTime(kDoubleLayout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/startPosition
            var ctrlTouchscreentouch4startPosition = Initialize_ctrlTouchscreentouch4startPosition(kVector2Layout, ctrlTouchscreentouch4);

            // /Touchscreen/touch4/position/x
            var ctrlTouchscreentouch4positionx = Initialize_ctrlTouchscreentouch4positionx(kAxisLayout, ctrlTouchscreentouch4position);

            // /Touchscreen/touch4/position/y
            var ctrlTouchscreentouch4positiony = Initialize_ctrlTouchscreentouch4positiony(kAxisLayout, ctrlTouchscreentouch4position);

            // /Touchscreen/touch4/delta/up
            var ctrlTouchscreentouch4deltaup = Initialize_ctrlTouchscreentouch4deltaup(kAxisLayout, ctrlTouchscreentouch4delta);

            // /Touchscreen/touch4/delta/down
            var ctrlTouchscreentouch4deltadown = Initialize_ctrlTouchscreentouch4deltadown(kAxisLayout, ctrlTouchscreentouch4delta);

            // /Touchscreen/touch4/delta/left
            var ctrlTouchscreentouch4deltaleft = Initialize_ctrlTouchscreentouch4deltaleft(kAxisLayout, ctrlTouchscreentouch4delta);

            // /Touchscreen/touch4/delta/right
            var ctrlTouchscreentouch4deltaright = Initialize_ctrlTouchscreentouch4deltaright(kAxisLayout, ctrlTouchscreentouch4delta);

            // /Touchscreen/touch4/delta/x
            var ctrlTouchscreentouch4deltax = Initialize_ctrlTouchscreentouch4deltax(kAxisLayout, ctrlTouchscreentouch4delta);

            // /Touchscreen/touch4/delta/y
            var ctrlTouchscreentouch4deltay = Initialize_ctrlTouchscreentouch4deltay(kAxisLayout, ctrlTouchscreentouch4delta);

            // /Touchscreen/touch4/radius/x
            var ctrlTouchscreentouch4radiusx = Initialize_ctrlTouchscreentouch4radiusx(kAxisLayout, ctrlTouchscreentouch4radius);

            // /Touchscreen/touch4/radius/y
            var ctrlTouchscreentouch4radiusy = Initialize_ctrlTouchscreentouch4radiusy(kAxisLayout, ctrlTouchscreentouch4radius);

            // /Touchscreen/touch4/startPosition/x
            var ctrlTouchscreentouch4startPositionx = Initialize_ctrlTouchscreentouch4startPositionx(kAxisLayout, ctrlTouchscreentouch4startPosition);

            // /Touchscreen/touch4/startPosition/y
            var ctrlTouchscreentouch4startPositiony = Initialize_ctrlTouchscreentouch4startPositiony(kAxisLayout, ctrlTouchscreentouch4startPosition);

            // /Touchscreen/touch5/touchId
            var ctrlTouchscreentouch5touchId = Initialize_ctrlTouchscreentouch5touchId(kIntegerLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/position
            var ctrlTouchscreentouch5position = Initialize_ctrlTouchscreentouch5position(kVector2Layout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/delta
            var ctrlTouchscreentouch5delta = Initialize_ctrlTouchscreentouch5delta(kDeltaLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/pressure
            var ctrlTouchscreentouch5pressure = Initialize_ctrlTouchscreentouch5pressure(kAxisLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/radius
            var ctrlTouchscreentouch5radius = Initialize_ctrlTouchscreentouch5radius(kVector2Layout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/phase
            var ctrlTouchscreentouch5phase = Initialize_ctrlTouchscreentouch5phase(kTouchPhaseLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/press
            var ctrlTouchscreentouch5press = Initialize_ctrlTouchscreentouch5press(kTouchPressLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/tapCount
            var ctrlTouchscreentouch5tapCount = Initialize_ctrlTouchscreentouch5tapCount(kIntegerLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/displayIndex
            var ctrlTouchscreentouch5displayIndex = Initialize_ctrlTouchscreentouch5displayIndex(kIntegerLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/indirectTouch
            var ctrlTouchscreentouch5indirectTouch = Initialize_ctrlTouchscreentouch5indirectTouch(kButtonLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/tap
            var ctrlTouchscreentouch5tap = Initialize_ctrlTouchscreentouch5tap(kButtonLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/startTime
            var ctrlTouchscreentouch5startTime = Initialize_ctrlTouchscreentouch5startTime(kDoubleLayout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/startPosition
            var ctrlTouchscreentouch5startPosition = Initialize_ctrlTouchscreentouch5startPosition(kVector2Layout, ctrlTouchscreentouch5);

            // /Touchscreen/touch5/position/x
            var ctrlTouchscreentouch5positionx = Initialize_ctrlTouchscreentouch5positionx(kAxisLayout, ctrlTouchscreentouch5position);

            // /Touchscreen/touch5/position/y
            var ctrlTouchscreentouch5positiony = Initialize_ctrlTouchscreentouch5positiony(kAxisLayout, ctrlTouchscreentouch5position);

            // /Touchscreen/touch5/delta/up
            var ctrlTouchscreentouch5deltaup = Initialize_ctrlTouchscreentouch5deltaup(kAxisLayout, ctrlTouchscreentouch5delta);

            // /Touchscreen/touch5/delta/down
            var ctrlTouchscreentouch5deltadown = Initialize_ctrlTouchscreentouch5deltadown(kAxisLayout, ctrlTouchscreentouch5delta);

            // /Touchscreen/touch5/delta/left
            var ctrlTouchscreentouch5deltaleft = Initialize_ctrlTouchscreentouch5deltaleft(kAxisLayout, ctrlTouchscreentouch5delta);

            // /Touchscreen/touch5/delta/right
            var ctrlTouchscreentouch5deltaright = Initialize_ctrlTouchscreentouch5deltaright(kAxisLayout, ctrlTouchscreentouch5delta);

            // /Touchscreen/touch5/delta/x
            var ctrlTouchscreentouch5deltax = Initialize_ctrlTouchscreentouch5deltax(kAxisLayout, ctrlTouchscreentouch5delta);

            // /Touchscreen/touch5/delta/y
            var ctrlTouchscreentouch5deltay = Initialize_ctrlTouchscreentouch5deltay(kAxisLayout, ctrlTouchscreentouch5delta);

            // /Touchscreen/touch5/radius/x
            var ctrlTouchscreentouch5radiusx = Initialize_ctrlTouchscreentouch5radiusx(kAxisLayout, ctrlTouchscreentouch5radius);

            // /Touchscreen/touch5/radius/y
            var ctrlTouchscreentouch5radiusy = Initialize_ctrlTouchscreentouch5radiusy(kAxisLayout, ctrlTouchscreentouch5radius);

            // /Touchscreen/touch5/startPosition/x
            var ctrlTouchscreentouch5startPositionx = Initialize_ctrlTouchscreentouch5startPositionx(kAxisLayout, ctrlTouchscreentouch5startPosition);

            // /Touchscreen/touch5/startPosition/y
            var ctrlTouchscreentouch5startPositiony = Initialize_ctrlTouchscreentouch5startPositiony(kAxisLayout, ctrlTouchscreentouch5startPosition);

            // /Touchscreen/touch6/touchId
            var ctrlTouchscreentouch6touchId = Initialize_ctrlTouchscreentouch6touchId(kIntegerLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/position
            var ctrlTouchscreentouch6position = Initialize_ctrlTouchscreentouch6position(kVector2Layout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/delta
            var ctrlTouchscreentouch6delta = Initialize_ctrlTouchscreentouch6delta(kDeltaLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/pressure
            var ctrlTouchscreentouch6pressure = Initialize_ctrlTouchscreentouch6pressure(kAxisLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/radius
            var ctrlTouchscreentouch6radius = Initialize_ctrlTouchscreentouch6radius(kVector2Layout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/phase
            var ctrlTouchscreentouch6phase = Initialize_ctrlTouchscreentouch6phase(kTouchPhaseLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/press
            var ctrlTouchscreentouch6press = Initialize_ctrlTouchscreentouch6press(kTouchPressLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/tapCount
            var ctrlTouchscreentouch6tapCount = Initialize_ctrlTouchscreentouch6tapCount(kIntegerLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/displayIndex
            var ctrlTouchscreentouch6displayIndex = Initialize_ctrlTouchscreentouch6displayIndex(kIntegerLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/indirectTouch
            var ctrlTouchscreentouch6indirectTouch = Initialize_ctrlTouchscreentouch6indirectTouch(kButtonLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/tap
            var ctrlTouchscreentouch6tap = Initialize_ctrlTouchscreentouch6tap(kButtonLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/startTime
            var ctrlTouchscreentouch6startTime = Initialize_ctrlTouchscreentouch6startTime(kDoubleLayout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/startPosition
            var ctrlTouchscreentouch6startPosition = Initialize_ctrlTouchscreentouch6startPosition(kVector2Layout, ctrlTouchscreentouch6);

            // /Touchscreen/touch6/position/x
            var ctrlTouchscreentouch6positionx = Initialize_ctrlTouchscreentouch6positionx(kAxisLayout, ctrlTouchscreentouch6position);

            // /Touchscreen/touch6/position/y
            var ctrlTouchscreentouch6positiony = Initialize_ctrlTouchscreentouch6positiony(kAxisLayout, ctrlTouchscreentouch6position);

            // /Touchscreen/touch6/delta/up
            var ctrlTouchscreentouch6deltaup = Initialize_ctrlTouchscreentouch6deltaup(kAxisLayout, ctrlTouchscreentouch6delta);

            // /Touchscreen/touch6/delta/down
            var ctrlTouchscreentouch6deltadown = Initialize_ctrlTouchscreentouch6deltadown(kAxisLayout, ctrlTouchscreentouch6delta);

            // /Touchscreen/touch6/delta/left
            var ctrlTouchscreentouch6deltaleft = Initialize_ctrlTouchscreentouch6deltaleft(kAxisLayout, ctrlTouchscreentouch6delta);

            // /Touchscreen/touch6/delta/right
            var ctrlTouchscreentouch6deltaright = Initialize_ctrlTouchscreentouch6deltaright(kAxisLayout, ctrlTouchscreentouch6delta);

            // /Touchscreen/touch6/delta/x
            var ctrlTouchscreentouch6deltax = Initialize_ctrlTouchscreentouch6deltax(kAxisLayout, ctrlTouchscreentouch6delta);

            // /Touchscreen/touch6/delta/y
            var ctrlTouchscreentouch6deltay = Initialize_ctrlTouchscreentouch6deltay(kAxisLayout, ctrlTouchscreentouch6delta);

            // /Touchscreen/touch6/radius/x
            var ctrlTouchscreentouch6radiusx = Initialize_ctrlTouchscreentouch6radiusx(kAxisLayout, ctrlTouchscreentouch6radius);

            // /Touchscreen/touch6/radius/y
            var ctrlTouchscreentouch6radiusy = Initialize_ctrlTouchscreentouch6radiusy(kAxisLayout, ctrlTouchscreentouch6radius);

            // /Touchscreen/touch6/startPosition/x
            var ctrlTouchscreentouch6startPositionx = Initialize_ctrlTouchscreentouch6startPositionx(kAxisLayout, ctrlTouchscreentouch6startPosition);

            // /Touchscreen/touch6/startPosition/y
            var ctrlTouchscreentouch6startPositiony = Initialize_ctrlTouchscreentouch6startPositiony(kAxisLayout, ctrlTouchscreentouch6startPosition);

            // /Touchscreen/touch7/touchId
            var ctrlTouchscreentouch7touchId = Initialize_ctrlTouchscreentouch7touchId(kIntegerLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/position
            var ctrlTouchscreentouch7position = Initialize_ctrlTouchscreentouch7position(kVector2Layout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/delta
            var ctrlTouchscreentouch7delta = Initialize_ctrlTouchscreentouch7delta(kDeltaLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/pressure
            var ctrlTouchscreentouch7pressure = Initialize_ctrlTouchscreentouch7pressure(kAxisLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/radius
            var ctrlTouchscreentouch7radius = Initialize_ctrlTouchscreentouch7radius(kVector2Layout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/phase
            var ctrlTouchscreentouch7phase = Initialize_ctrlTouchscreentouch7phase(kTouchPhaseLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/press
            var ctrlTouchscreentouch7press = Initialize_ctrlTouchscreentouch7press(kTouchPressLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/tapCount
            var ctrlTouchscreentouch7tapCount = Initialize_ctrlTouchscreentouch7tapCount(kIntegerLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/displayIndex
            var ctrlTouchscreentouch7displayIndex = Initialize_ctrlTouchscreentouch7displayIndex(kIntegerLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/indirectTouch
            var ctrlTouchscreentouch7indirectTouch = Initialize_ctrlTouchscreentouch7indirectTouch(kButtonLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/tap
            var ctrlTouchscreentouch7tap = Initialize_ctrlTouchscreentouch7tap(kButtonLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/startTime
            var ctrlTouchscreentouch7startTime = Initialize_ctrlTouchscreentouch7startTime(kDoubleLayout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/startPosition
            var ctrlTouchscreentouch7startPosition = Initialize_ctrlTouchscreentouch7startPosition(kVector2Layout, ctrlTouchscreentouch7);

            // /Touchscreen/touch7/position/x
            var ctrlTouchscreentouch7positionx = Initialize_ctrlTouchscreentouch7positionx(kAxisLayout, ctrlTouchscreentouch7position);

            // /Touchscreen/touch7/position/y
            var ctrlTouchscreentouch7positiony = Initialize_ctrlTouchscreentouch7positiony(kAxisLayout, ctrlTouchscreentouch7position);

            // /Touchscreen/touch7/delta/up
            var ctrlTouchscreentouch7deltaup = Initialize_ctrlTouchscreentouch7deltaup(kAxisLayout, ctrlTouchscreentouch7delta);

            // /Touchscreen/touch7/delta/down
            var ctrlTouchscreentouch7deltadown = Initialize_ctrlTouchscreentouch7deltadown(kAxisLayout, ctrlTouchscreentouch7delta);

            // /Touchscreen/touch7/delta/left
            var ctrlTouchscreentouch7deltaleft = Initialize_ctrlTouchscreentouch7deltaleft(kAxisLayout, ctrlTouchscreentouch7delta);

            // /Touchscreen/touch7/delta/right
            var ctrlTouchscreentouch7deltaright = Initialize_ctrlTouchscreentouch7deltaright(kAxisLayout, ctrlTouchscreentouch7delta);

            // /Touchscreen/touch7/delta/x
            var ctrlTouchscreentouch7deltax = Initialize_ctrlTouchscreentouch7deltax(kAxisLayout, ctrlTouchscreentouch7delta);

            // /Touchscreen/touch7/delta/y
            var ctrlTouchscreentouch7deltay = Initialize_ctrlTouchscreentouch7deltay(kAxisLayout, ctrlTouchscreentouch7delta);

            // /Touchscreen/touch7/radius/x
            var ctrlTouchscreentouch7radiusx = Initialize_ctrlTouchscreentouch7radiusx(kAxisLayout, ctrlTouchscreentouch7radius);

            // /Touchscreen/touch7/radius/y
            var ctrlTouchscreentouch7radiusy = Initialize_ctrlTouchscreentouch7radiusy(kAxisLayout, ctrlTouchscreentouch7radius);

            // /Touchscreen/touch7/startPosition/x
            var ctrlTouchscreentouch7startPositionx = Initialize_ctrlTouchscreentouch7startPositionx(kAxisLayout, ctrlTouchscreentouch7startPosition);

            // /Touchscreen/touch7/startPosition/y
            var ctrlTouchscreentouch7startPositiony = Initialize_ctrlTouchscreentouch7startPositiony(kAxisLayout, ctrlTouchscreentouch7startPosition);

            // /Touchscreen/touch8/touchId
            var ctrlTouchscreentouch8touchId = Initialize_ctrlTouchscreentouch8touchId(kIntegerLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/position
            var ctrlTouchscreentouch8position = Initialize_ctrlTouchscreentouch8position(kVector2Layout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/delta
            var ctrlTouchscreentouch8delta = Initialize_ctrlTouchscreentouch8delta(kDeltaLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/pressure
            var ctrlTouchscreentouch8pressure = Initialize_ctrlTouchscreentouch8pressure(kAxisLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/radius
            var ctrlTouchscreentouch8radius = Initialize_ctrlTouchscreentouch8radius(kVector2Layout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/phase
            var ctrlTouchscreentouch8phase = Initialize_ctrlTouchscreentouch8phase(kTouchPhaseLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/press
            var ctrlTouchscreentouch8press = Initialize_ctrlTouchscreentouch8press(kTouchPressLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/tapCount
            var ctrlTouchscreentouch8tapCount = Initialize_ctrlTouchscreentouch8tapCount(kIntegerLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/displayIndex
            var ctrlTouchscreentouch8displayIndex = Initialize_ctrlTouchscreentouch8displayIndex(kIntegerLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/indirectTouch
            var ctrlTouchscreentouch8indirectTouch = Initialize_ctrlTouchscreentouch8indirectTouch(kButtonLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/tap
            var ctrlTouchscreentouch8tap = Initialize_ctrlTouchscreentouch8tap(kButtonLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/startTime
            var ctrlTouchscreentouch8startTime = Initialize_ctrlTouchscreentouch8startTime(kDoubleLayout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/startPosition
            var ctrlTouchscreentouch8startPosition = Initialize_ctrlTouchscreentouch8startPosition(kVector2Layout, ctrlTouchscreentouch8);

            // /Touchscreen/touch8/position/x
            var ctrlTouchscreentouch8positionx = Initialize_ctrlTouchscreentouch8positionx(kAxisLayout, ctrlTouchscreentouch8position);

            // /Touchscreen/touch8/position/y
            var ctrlTouchscreentouch8positiony = Initialize_ctrlTouchscreentouch8positiony(kAxisLayout, ctrlTouchscreentouch8position);

            // /Touchscreen/touch8/delta/up
            var ctrlTouchscreentouch8deltaup = Initialize_ctrlTouchscreentouch8deltaup(kAxisLayout, ctrlTouchscreentouch8delta);

            // /Touchscreen/touch8/delta/down
            var ctrlTouchscreentouch8deltadown = Initialize_ctrlTouchscreentouch8deltadown(kAxisLayout, ctrlTouchscreentouch8delta);

            // /Touchscreen/touch8/delta/left
            var ctrlTouchscreentouch8deltaleft = Initialize_ctrlTouchscreentouch8deltaleft(kAxisLayout, ctrlTouchscreentouch8delta);

            // /Touchscreen/touch8/delta/right
            var ctrlTouchscreentouch8deltaright = Initialize_ctrlTouchscreentouch8deltaright(kAxisLayout, ctrlTouchscreentouch8delta);

            // /Touchscreen/touch8/delta/x
            var ctrlTouchscreentouch8deltax = Initialize_ctrlTouchscreentouch8deltax(kAxisLayout, ctrlTouchscreentouch8delta);

            // /Touchscreen/touch8/delta/y
            var ctrlTouchscreentouch8deltay = Initialize_ctrlTouchscreentouch8deltay(kAxisLayout, ctrlTouchscreentouch8delta);

            // /Touchscreen/touch8/radius/x
            var ctrlTouchscreentouch8radiusx = Initialize_ctrlTouchscreentouch8radiusx(kAxisLayout, ctrlTouchscreentouch8radius);

            // /Touchscreen/touch8/radius/y
            var ctrlTouchscreentouch8radiusy = Initialize_ctrlTouchscreentouch8radiusy(kAxisLayout, ctrlTouchscreentouch8radius);

            // /Touchscreen/touch8/startPosition/x
            var ctrlTouchscreentouch8startPositionx = Initialize_ctrlTouchscreentouch8startPositionx(kAxisLayout, ctrlTouchscreentouch8startPosition);

            // /Touchscreen/touch8/startPosition/y
            var ctrlTouchscreentouch8startPositiony = Initialize_ctrlTouchscreentouch8startPositiony(kAxisLayout, ctrlTouchscreentouch8startPosition);

            // /Touchscreen/touch9/touchId
            var ctrlTouchscreentouch9touchId = Initialize_ctrlTouchscreentouch9touchId(kIntegerLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/position
            var ctrlTouchscreentouch9position = Initialize_ctrlTouchscreentouch9position(kVector2Layout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/delta
            var ctrlTouchscreentouch9delta = Initialize_ctrlTouchscreentouch9delta(kDeltaLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/pressure
            var ctrlTouchscreentouch9pressure = Initialize_ctrlTouchscreentouch9pressure(kAxisLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/radius
            var ctrlTouchscreentouch9radius = Initialize_ctrlTouchscreentouch9radius(kVector2Layout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/phase
            var ctrlTouchscreentouch9phase = Initialize_ctrlTouchscreentouch9phase(kTouchPhaseLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/press
            var ctrlTouchscreentouch9press = Initialize_ctrlTouchscreentouch9press(kTouchPressLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/tapCount
            var ctrlTouchscreentouch9tapCount = Initialize_ctrlTouchscreentouch9tapCount(kIntegerLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/displayIndex
            var ctrlTouchscreentouch9displayIndex = Initialize_ctrlTouchscreentouch9displayIndex(kIntegerLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/indirectTouch
            var ctrlTouchscreentouch9indirectTouch = Initialize_ctrlTouchscreentouch9indirectTouch(kButtonLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/tap
            var ctrlTouchscreentouch9tap = Initialize_ctrlTouchscreentouch9tap(kButtonLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/startTime
            var ctrlTouchscreentouch9startTime = Initialize_ctrlTouchscreentouch9startTime(kDoubleLayout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/startPosition
            var ctrlTouchscreentouch9startPosition = Initialize_ctrlTouchscreentouch9startPosition(kVector2Layout, ctrlTouchscreentouch9);

            // /Touchscreen/touch9/position/x
            var ctrlTouchscreentouch9positionx = Initialize_ctrlTouchscreentouch9positionx(kAxisLayout, ctrlTouchscreentouch9position);

            // /Touchscreen/touch9/position/y
            var ctrlTouchscreentouch9positiony = Initialize_ctrlTouchscreentouch9positiony(kAxisLayout, ctrlTouchscreentouch9position);

            // /Touchscreen/touch9/delta/up
            var ctrlTouchscreentouch9deltaup = Initialize_ctrlTouchscreentouch9deltaup(kAxisLayout, ctrlTouchscreentouch9delta);

            // /Touchscreen/touch9/delta/down
            var ctrlTouchscreentouch9deltadown = Initialize_ctrlTouchscreentouch9deltadown(kAxisLayout, ctrlTouchscreentouch9delta);

            // /Touchscreen/touch9/delta/left
            var ctrlTouchscreentouch9deltaleft = Initialize_ctrlTouchscreentouch9deltaleft(kAxisLayout, ctrlTouchscreentouch9delta);

            // /Touchscreen/touch9/delta/right
            var ctrlTouchscreentouch9deltaright = Initialize_ctrlTouchscreentouch9deltaright(kAxisLayout, ctrlTouchscreentouch9delta);

            // /Touchscreen/touch9/delta/x
            var ctrlTouchscreentouch9deltax = Initialize_ctrlTouchscreentouch9deltax(kAxisLayout, ctrlTouchscreentouch9delta);

            // /Touchscreen/touch9/delta/y
            var ctrlTouchscreentouch9deltay = Initialize_ctrlTouchscreentouch9deltay(kAxisLayout, ctrlTouchscreentouch9delta);

            // /Touchscreen/touch9/radius/x
            var ctrlTouchscreentouch9radiusx = Initialize_ctrlTouchscreentouch9radiusx(kAxisLayout, ctrlTouchscreentouch9radius);

            // /Touchscreen/touch9/radius/y
            var ctrlTouchscreentouch9radiusy = Initialize_ctrlTouchscreentouch9radiusy(kAxisLayout, ctrlTouchscreentouch9radius);

            // /Touchscreen/touch9/startPosition/x
            var ctrlTouchscreentouch9startPositionx = Initialize_ctrlTouchscreentouch9startPositionx(kAxisLayout, ctrlTouchscreentouch9startPosition);

            // /Touchscreen/touch9/startPosition/y
            var ctrlTouchscreentouch9startPositiony = Initialize_ctrlTouchscreentouch9startPositiony(kAxisLayout, ctrlTouchscreentouch9startPosition);

            // Usages.
            builder.WithControlUsage(0, new InternedString("PrimaryAction"), ctrlTouchscreenprimaryTouchtap);
            builder.WithControlUsage(1, new InternedString("Point"), ctrlTouchscreenposition);
            builder.WithControlUsage(2, new InternedString("Secondary2DMotion"), ctrlTouchscreendelta);
            builder.WithControlUsage(3, new InternedString("Pressure"), ctrlTouchscreenpressure);
            builder.WithControlUsage(4, new InternedString("Radius"), ctrlTouchscreenradius);

            // Control getters/arrays.
            this.touchControlArray = new UnityEngine.InputSystem.Controls.TouchControl[10];
            this.touchControlArray[0] = ctrlTouchscreentouch0;
            this.touchControlArray[1] = ctrlTouchscreentouch1;
            this.touchControlArray[2] = ctrlTouchscreentouch2;
            this.touchControlArray[3] = ctrlTouchscreentouch3;
            this.touchControlArray[4] = ctrlTouchscreentouch4;
            this.touchControlArray[5] = ctrlTouchscreentouch5;
            this.touchControlArray[6] = ctrlTouchscreentouch6;
            this.touchControlArray[7] = ctrlTouchscreentouch7;
            this.touchControlArray[8] = ctrlTouchscreentouch8;
            this.touchControlArray[9] = ctrlTouchscreentouch9;
            this.primaryTouch = ctrlTouchscreenprimaryTouch;
            this.position = ctrlTouchscreenposition;
            this.delta = ctrlTouchscreendelta;
            this.radius = ctrlTouchscreenradius;
            this.pressure = ctrlTouchscreenpressure;
            this.press = ctrlTouchscreenpress;
            this.displayIndex = ctrlTouchscreendisplayIndex;
            ctrlTouchscreenprimaryTouch.press = ctrlTouchscreenprimaryTouchpress;
            ctrlTouchscreenprimaryTouch.displayIndex = ctrlTouchscreenprimaryTouchdisplayIndex;
            ctrlTouchscreenprimaryTouch.touchId = ctrlTouchscreenprimaryTouchtouchId;
            ctrlTouchscreenprimaryTouch.position = ctrlTouchscreenprimaryTouchposition;
            ctrlTouchscreenprimaryTouch.delta = ctrlTouchscreenprimaryTouchdelta;
            ctrlTouchscreenprimaryTouch.pressure = ctrlTouchscreenprimaryTouchpressure;
            ctrlTouchscreenprimaryTouch.radius = ctrlTouchscreenprimaryTouchradius;
            ctrlTouchscreenprimaryTouch.phase = ctrlTouchscreenprimaryTouchphase;
            ctrlTouchscreenprimaryTouch.indirectTouch = ctrlTouchscreenprimaryTouchindirectTouch;
            ctrlTouchscreenprimaryTouch.tap = ctrlTouchscreenprimaryTouchtap;
            ctrlTouchscreenprimaryTouch.tapCount = ctrlTouchscreenprimaryTouchtapCount;
            ctrlTouchscreenprimaryTouch.startTime = ctrlTouchscreenprimaryTouchstartTime;
            ctrlTouchscreenprimaryTouch.startPosition = ctrlTouchscreenprimaryTouchstartPosition;
            ctrlTouchscreenposition.x = ctrlTouchscreenpositionx;
            ctrlTouchscreenposition.y = ctrlTouchscreenpositiony;
            ctrlTouchscreendelta.up = ctrlTouchscreendeltaup;
            ctrlTouchscreendelta.down = ctrlTouchscreendeltadown;
            ctrlTouchscreendelta.left = ctrlTouchscreendeltaleft;
            ctrlTouchscreendelta.right = ctrlTouchscreendeltaright;
            ctrlTouchscreendelta.x = ctrlTouchscreendeltax;
            ctrlTouchscreendelta.y = ctrlTouchscreendeltay;
            ctrlTouchscreenradius.x = ctrlTouchscreenradiusx;
            ctrlTouchscreenradius.y = ctrlTouchscreenradiusy;
            ctrlTouchscreentouch0.press = ctrlTouchscreentouch0press;
            ctrlTouchscreentouch0.displayIndex = ctrlTouchscreentouch0displayIndex;
            ctrlTouchscreentouch0.touchId = ctrlTouchscreentouch0touchId;
            ctrlTouchscreentouch0.position = ctrlTouchscreentouch0position;
            ctrlTouchscreentouch0.delta = ctrlTouchscreentouch0delta;
            ctrlTouchscreentouch0.pressure = ctrlTouchscreentouch0pressure;
            ctrlTouchscreentouch0.radius = ctrlTouchscreentouch0radius;
            ctrlTouchscreentouch0.phase = ctrlTouchscreentouch0phase;
            ctrlTouchscreentouch0.indirectTouch = ctrlTouchscreentouch0indirectTouch;
            ctrlTouchscreentouch0.tap = ctrlTouchscreentouch0tap;
            ctrlTouchscreentouch0.tapCount = ctrlTouchscreentouch0tapCount;
            ctrlTouchscreentouch0.startTime = ctrlTouchscreentouch0startTime;
            ctrlTouchscreentouch0.startPosition = ctrlTouchscreentouch0startPosition;
            ctrlTouchscreentouch1.press = ctrlTouchscreentouch1press;
            ctrlTouchscreentouch1.displayIndex = ctrlTouchscreentouch1displayIndex;
            ctrlTouchscreentouch1.touchId = ctrlTouchscreentouch1touchId;
            ctrlTouchscreentouch1.position = ctrlTouchscreentouch1position;
            ctrlTouchscreentouch1.delta = ctrlTouchscreentouch1delta;
            ctrlTouchscreentouch1.pressure = ctrlTouchscreentouch1pressure;
            ctrlTouchscreentouch1.radius = ctrlTouchscreentouch1radius;
            ctrlTouchscreentouch1.phase = ctrlTouchscreentouch1phase;
            ctrlTouchscreentouch1.indirectTouch = ctrlTouchscreentouch1indirectTouch;
            ctrlTouchscreentouch1.tap = ctrlTouchscreentouch1tap;
            ctrlTouchscreentouch1.tapCount = ctrlTouchscreentouch1tapCount;
            ctrlTouchscreentouch1.startTime = ctrlTouchscreentouch1startTime;
            ctrlTouchscreentouch1.startPosition = ctrlTouchscreentouch1startPosition;
            ctrlTouchscreentouch2.press = ctrlTouchscreentouch2press;
            ctrlTouchscreentouch2.displayIndex = ctrlTouchscreentouch2displayIndex;
            ctrlTouchscreentouch2.touchId = ctrlTouchscreentouch2touchId;
            ctrlTouchscreentouch2.position = ctrlTouchscreentouch2position;
            ctrlTouchscreentouch2.delta = ctrlTouchscreentouch2delta;
            ctrlTouchscreentouch2.pressure = ctrlTouchscreentouch2pressure;
            ctrlTouchscreentouch2.radius = ctrlTouchscreentouch2radius;
            ctrlTouchscreentouch2.phase = ctrlTouchscreentouch2phase;
            ctrlTouchscreentouch2.indirectTouch = ctrlTouchscreentouch2indirectTouch;
            ctrlTouchscreentouch2.tap = ctrlTouchscreentouch2tap;
            ctrlTouchscreentouch2.tapCount = ctrlTouchscreentouch2tapCount;
            ctrlTouchscreentouch2.startTime = ctrlTouchscreentouch2startTime;
            ctrlTouchscreentouch2.startPosition = ctrlTouchscreentouch2startPosition;
            ctrlTouchscreentouch3.press = ctrlTouchscreentouch3press;
            ctrlTouchscreentouch3.displayIndex = ctrlTouchscreentouch3displayIndex;
            ctrlTouchscreentouch3.touchId = ctrlTouchscreentouch3touchId;
            ctrlTouchscreentouch3.position = ctrlTouchscreentouch3position;
            ctrlTouchscreentouch3.delta = ctrlTouchscreentouch3delta;
            ctrlTouchscreentouch3.pressure = ctrlTouchscreentouch3pressure;
            ctrlTouchscreentouch3.radius = ctrlTouchscreentouch3radius;
            ctrlTouchscreentouch3.phase = ctrlTouchscreentouch3phase;
            ctrlTouchscreentouch3.indirectTouch = ctrlTouchscreentouch3indirectTouch;
            ctrlTouchscreentouch3.tap = ctrlTouchscreentouch3tap;
            ctrlTouchscreentouch3.tapCount = ctrlTouchscreentouch3tapCount;
            ctrlTouchscreentouch3.startTime = ctrlTouchscreentouch3startTime;
            ctrlTouchscreentouch3.startPosition = ctrlTouchscreentouch3startPosition;
            ctrlTouchscreentouch4.press = ctrlTouchscreentouch4press;
            ctrlTouchscreentouch4.displayIndex = ctrlTouchscreentouch4displayIndex;
            ctrlTouchscreentouch4.touchId = ctrlTouchscreentouch4touchId;
            ctrlTouchscreentouch4.position = ctrlTouchscreentouch4position;
            ctrlTouchscreentouch4.delta = ctrlTouchscreentouch4delta;
            ctrlTouchscreentouch4.pressure = ctrlTouchscreentouch4pressure;
            ctrlTouchscreentouch4.radius = ctrlTouchscreentouch4radius;
            ctrlTouchscreentouch4.phase = ctrlTouchscreentouch4phase;
            ctrlTouchscreentouch4.indirectTouch = ctrlTouchscreentouch4indirectTouch;
            ctrlTouchscreentouch4.tap = ctrlTouchscreentouch4tap;
            ctrlTouchscreentouch4.tapCount = ctrlTouchscreentouch4tapCount;
            ctrlTouchscreentouch4.startTime = ctrlTouchscreentouch4startTime;
            ctrlTouchscreentouch4.startPosition = ctrlTouchscreentouch4startPosition;
            ctrlTouchscreentouch5.press = ctrlTouchscreentouch5press;
            ctrlTouchscreentouch5.displayIndex = ctrlTouchscreentouch5displayIndex;
            ctrlTouchscreentouch5.touchId = ctrlTouchscreentouch5touchId;
            ctrlTouchscreentouch5.position = ctrlTouchscreentouch5position;
            ctrlTouchscreentouch5.delta = ctrlTouchscreentouch5delta;
            ctrlTouchscreentouch5.pressure = ctrlTouchscreentouch5pressure;
            ctrlTouchscreentouch5.radius = ctrlTouchscreentouch5radius;
            ctrlTouchscreentouch5.phase = ctrlTouchscreentouch5phase;
            ctrlTouchscreentouch5.indirectTouch = ctrlTouchscreentouch5indirectTouch;
            ctrlTouchscreentouch5.tap = ctrlTouchscreentouch5tap;
            ctrlTouchscreentouch5.tapCount = ctrlTouchscreentouch5tapCount;
            ctrlTouchscreentouch5.startTime = ctrlTouchscreentouch5startTime;
            ctrlTouchscreentouch5.startPosition = ctrlTouchscreentouch5startPosition;
            ctrlTouchscreentouch6.press = ctrlTouchscreentouch6press;
            ctrlTouchscreentouch6.displayIndex = ctrlTouchscreentouch6displayIndex;
            ctrlTouchscreentouch6.touchId = ctrlTouchscreentouch6touchId;
            ctrlTouchscreentouch6.position = ctrlTouchscreentouch6position;
            ctrlTouchscreentouch6.delta = ctrlTouchscreentouch6delta;
            ctrlTouchscreentouch6.pressure = ctrlTouchscreentouch6pressure;
            ctrlTouchscreentouch6.radius = ctrlTouchscreentouch6radius;
            ctrlTouchscreentouch6.phase = ctrlTouchscreentouch6phase;
            ctrlTouchscreentouch6.indirectTouch = ctrlTouchscreentouch6indirectTouch;
            ctrlTouchscreentouch6.tap = ctrlTouchscreentouch6tap;
            ctrlTouchscreentouch6.tapCount = ctrlTouchscreentouch6tapCount;
            ctrlTouchscreentouch6.startTime = ctrlTouchscreentouch6startTime;
            ctrlTouchscreentouch6.startPosition = ctrlTouchscreentouch6startPosition;
            ctrlTouchscreentouch7.press = ctrlTouchscreentouch7press;
            ctrlTouchscreentouch7.displayIndex = ctrlTouchscreentouch7displayIndex;
            ctrlTouchscreentouch7.touchId = ctrlTouchscreentouch7touchId;
            ctrlTouchscreentouch7.position = ctrlTouchscreentouch7position;
            ctrlTouchscreentouch7.delta = ctrlTouchscreentouch7delta;
            ctrlTouchscreentouch7.pressure = ctrlTouchscreentouch7pressure;
            ctrlTouchscreentouch7.radius = ctrlTouchscreentouch7radius;
            ctrlTouchscreentouch7.phase = ctrlTouchscreentouch7phase;
            ctrlTouchscreentouch7.indirectTouch = ctrlTouchscreentouch7indirectTouch;
            ctrlTouchscreentouch7.tap = ctrlTouchscreentouch7tap;
            ctrlTouchscreentouch7.tapCount = ctrlTouchscreentouch7tapCount;
            ctrlTouchscreentouch7.startTime = ctrlTouchscreentouch7startTime;
            ctrlTouchscreentouch7.startPosition = ctrlTouchscreentouch7startPosition;
            ctrlTouchscreentouch8.press = ctrlTouchscreentouch8press;
            ctrlTouchscreentouch8.displayIndex = ctrlTouchscreentouch8displayIndex;
            ctrlTouchscreentouch8.touchId = ctrlTouchscreentouch8touchId;
            ctrlTouchscreentouch8.position = ctrlTouchscreentouch8position;
            ctrlTouchscreentouch8.delta = ctrlTouchscreentouch8delta;
            ctrlTouchscreentouch8.pressure = ctrlTouchscreentouch8pressure;
            ctrlTouchscreentouch8.radius = ctrlTouchscreentouch8radius;
            ctrlTouchscreentouch8.phase = ctrlTouchscreentouch8phase;
            ctrlTouchscreentouch8.indirectTouch = ctrlTouchscreentouch8indirectTouch;
            ctrlTouchscreentouch8.tap = ctrlTouchscreentouch8tap;
            ctrlTouchscreentouch8.tapCount = ctrlTouchscreentouch8tapCount;
            ctrlTouchscreentouch8.startTime = ctrlTouchscreentouch8startTime;
            ctrlTouchscreentouch8.startPosition = ctrlTouchscreentouch8startPosition;
            ctrlTouchscreentouch9.press = ctrlTouchscreentouch9press;
            ctrlTouchscreentouch9.displayIndex = ctrlTouchscreentouch9displayIndex;
            ctrlTouchscreentouch9.touchId = ctrlTouchscreentouch9touchId;
            ctrlTouchscreentouch9.position = ctrlTouchscreentouch9position;
            ctrlTouchscreentouch9.delta = ctrlTouchscreentouch9delta;
            ctrlTouchscreentouch9.pressure = ctrlTouchscreentouch9pressure;
            ctrlTouchscreentouch9.radius = ctrlTouchscreentouch9radius;
            ctrlTouchscreentouch9.phase = ctrlTouchscreentouch9phase;
            ctrlTouchscreentouch9.indirectTouch = ctrlTouchscreentouch9indirectTouch;
            ctrlTouchscreentouch9.tap = ctrlTouchscreentouch9tap;
            ctrlTouchscreentouch9.tapCount = ctrlTouchscreentouch9tapCount;
            ctrlTouchscreentouch9.startTime = ctrlTouchscreentouch9startTime;
            ctrlTouchscreentouch9.startPosition = ctrlTouchscreentouch9startPosition;
            ctrlTouchscreenprimaryTouchposition.x = ctrlTouchscreenprimaryTouchpositionx;
            ctrlTouchscreenprimaryTouchposition.y = ctrlTouchscreenprimaryTouchpositiony;
            ctrlTouchscreenprimaryTouchdelta.up = ctrlTouchscreenprimaryTouchdeltaup;
            ctrlTouchscreenprimaryTouchdelta.down = ctrlTouchscreenprimaryTouchdeltadown;
            ctrlTouchscreenprimaryTouchdelta.left = ctrlTouchscreenprimaryTouchdeltaleft;
            ctrlTouchscreenprimaryTouchdelta.right = ctrlTouchscreenprimaryTouchdeltaright;
            ctrlTouchscreenprimaryTouchdelta.x = ctrlTouchscreenprimaryTouchdeltax;
            ctrlTouchscreenprimaryTouchdelta.y = ctrlTouchscreenprimaryTouchdeltay;
            ctrlTouchscreenprimaryTouchradius.x = ctrlTouchscreenprimaryTouchradiusx;
            ctrlTouchscreenprimaryTouchradius.y = ctrlTouchscreenprimaryTouchradiusy;
            ctrlTouchscreenprimaryTouchstartPosition.x = ctrlTouchscreenprimaryTouchstartPositionx;
            ctrlTouchscreenprimaryTouchstartPosition.y = ctrlTouchscreenprimaryTouchstartPositiony;
            ctrlTouchscreentouch0position.x = ctrlTouchscreentouch0positionx;
            ctrlTouchscreentouch0position.y = ctrlTouchscreentouch0positiony;
            ctrlTouchscreentouch0delta.up = ctrlTouchscreentouch0deltaup;
            ctrlTouchscreentouch0delta.down = ctrlTouchscreentouch0deltadown;
            ctrlTouchscreentouch0delta.left = ctrlTouchscreentouch0deltaleft;
            ctrlTouchscreentouch0delta.right = ctrlTouchscreentouch0deltaright;
            ctrlTouchscreentouch0delta.x = ctrlTouchscreentouch0deltax;
            ctrlTouchscreentouch0delta.y = ctrlTouchscreentouch0deltay;
            ctrlTouchscreentouch0radius.x = ctrlTouchscreentouch0radiusx;
            ctrlTouchscreentouch0radius.y = ctrlTouchscreentouch0radiusy;
            ctrlTouchscreentouch0startPosition.x = ctrlTouchscreentouch0startPositionx;
            ctrlTouchscreentouch0startPosition.y = ctrlTouchscreentouch0startPositiony;
            ctrlTouchscreentouch1position.x = ctrlTouchscreentouch1positionx;
            ctrlTouchscreentouch1position.y = ctrlTouchscreentouch1positiony;
            ctrlTouchscreentouch1delta.up = ctrlTouchscreentouch1deltaup;
            ctrlTouchscreentouch1delta.down = ctrlTouchscreentouch1deltadown;
            ctrlTouchscreentouch1delta.left = ctrlTouchscreentouch1deltaleft;
            ctrlTouchscreentouch1delta.right = ctrlTouchscreentouch1deltaright;
            ctrlTouchscreentouch1delta.x = ctrlTouchscreentouch1deltax;
            ctrlTouchscreentouch1delta.y = ctrlTouchscreentouch1deltay;
            ctrlTouchscreentouch1radius.x = ctrlTouchscreentouch1radiusx;
            ctrlTouchscreentouch1radius.y = ctrlTouchscreentouch1radiusy;
            ctrlTouchscreentouch1startPosition.x = ctrlTouchscreentouch1startPositionx;
            ctrlTouchscreentouch1startPosition.y = ctrlTouchscreentouch1startPositiony;
            ctrlTouchscreentouch2position.x = ctrlTouchscreentouch2positionx;
            ctrlTouchscreentouch2position.y = ctrlTouchscreentouch2positiony;
            ctrlTouchscreentouch2delta.up = ctrlTouchscreentouch2deltaup;
            ctrlTouchscreentouch2delta.down = ctrlTouchscreentouch2deltadown;
            ctrlTouchscreentouch2delta.left = ctrlTouchscreentouch2deltaleft;
            ctrlTouchscreentouch2delta.right = ctrlTouchscreentouch2deltaright;
            ctrlTouchscreentouch2delta.x = ctrlTouchscreentouch2deltax;
            ctrlTouchscreentouch2delta.y = ctrlTouchscreentouch2deltay;
            ctrlTouchscreentouch2radius.x = ctrlTouchscreentouch2radiusx;
            ctrlTouchscreentouch2radius.y = ctrlTouchscreentouch2radiusy;
            ctrlTouchscreentouch2startPosition.x = ctrlTouchscreentouch2startPositionx;
            ctrlTouchscreentouch2startPosition.y = ctrlTouchscreentouch2startPositiony;
            ctrlTouchscreentouch3position.x = ctrlTouchscreentouch3positionx;
            ctrlTouchscreentouch3position.y = ctrlTouchscreentouch3positiony;
            ctrlTouchscreentouch3delta.up = ctrlTouchscreentouch3deltaup;
            ctrlTouchscreentouch3delta.down = ctrlTouchscreentouch3deltadown;
            ctrlTouchscreentouch3delta.left = ctrlTouchscreentouch3deltaleft;
            ctrlTouchscreentouch3delta.right = ctrlTouchscreentouch3deltaright;
            ctrlTouchscreentouch3delta.x = ctrlTouchscreentouch3deltax;
            ctrlTouchscreentouch3delta.y = ctrlTouchscreentouch3deltay;
            ctrlTouchscreentouch3radius.x = ctrlTouchscreentouch3radiusx;
            ctrlTouchscreentouch3radius.y = ctrlTouchscreentouch3radiusy;
            ctrlTouchscreentouch3startPosition.x = ctrlTouchscreentouch3startPositionx;
            ctrlTouchscreentouch3startPosition.y = ctrlTouchscreentouch3startPositiony;
            ctrlTouchscreentouch4position.x = ctrlTouchscreentouch4positionx;
            ctrlTouchscreentouch4position.y = ctrlTouchscreentouch4positiony;
            ctrlTouchscreentouch4delta.up = ctrlTouchscreentouch4deltaup;
            ctrlTouchscreentouch4delta.down = ctrlTouchscreentouch4deltadown;
            ctrlTouchscreentouch4delta.left = ctrlTouchscreentouch4deltaleft;
            ctrlTouchscreentouch4delta.right = ctrlTouchscreentouch4deltaright;
            ctrlTouchscreentouch4delta.x = ctrlTouchscreentouch4deltax;
            ctrlTouchscreentouch4delta.y = ctrlTouchscreentouch4deltay;
            ctrlTouchscreentouch4radius.x = ctrlTouchscreentouch4radiusx;
            ctrlTouchscreentouch4radius.y = ctrlTouchscreentouch4radiusy;
            ctrlTouchscreentouch4startPosition.x = ctrlTouchscreentouch4startPositionx;
            ctrlTouchscreentouch4startPosition.y = ctrlTouchscreentouch4startPositiony;
            ctrlTouchscreentouch5position.x = ctrlTouchscreentouch5positionx;
            ctrlTouchscreentouch5position.y = ctrlTouchscreentouch5positiony;
            ctrlTouchscreentouch5delta.up = ctrlTouchscreentouch5deltaup;
            ctrlTouchscreentouch5delta.down = ctrlTouchscreentouch5deltadown;
            ctrlTouchscreentouch5delta.left = ctrlTouchscreentouch5deltaleft;
            ctrlTouchscreentouch5delta.right = ctrlTouchscreentouch5deltaright;
            ctrlTouchscreentouch5delta.x = ctrlTouchscreentouch5deltax;
            ctrlTouchscreentouch5delta.y = ctrlTouchscreentouch5deltay;
            ctrlTouchscreentouch5radius.x = ctrlTouchscreentouch5radiusx;
            ctrlTouchscreentouch5radius.y = ctrlTouchscreentouch5radiusy;
            ctrlTouchscreentouch5startPosition.x = ctrlTouchscreentouch5startPositionx;
            ctrlTouchscreentouch5startPosition.y = ctrlTouchscreentouch5startPositiony;
            ctrlTouchscreentouch6position.x = ctrlTouchscreentouch6positionx;
            ctrlTouchscreentouch6position.y = ctrlTouchscreentouch6positiony;
            ctrlTouchscreentouch6delta.up = ctrlTouchscreentouch6deltaup;
            ctrlTouchscreentouch6delta.down = ctrlTouchscreentouch6deltadown;
            ctrlTouchscreentouch6delta.left = ctrlTouchscreentouch6deltaleft;
            ctrlTouchscreentouch6delta.right = ctrlTouchscreentouch6deltaright;
            ctrlTouchscreentouch6delta.x = ctrlTouchscreentouch6deltax;
            ctrlTouchscreentouch6delta.y = ctrlTouchscreentouch6deltay;
            ctrlTouchscreentouch6radius.x = ctrlTouchscreentouch6radiusx;
            ctrlTouchscreentouch6radius.y = ctrlTouchscreentouch6radiusy;
            ctrlTouchscreentouch6startPosition.x = ctrlTouchscreentouch6startPositionx;
            ctrlTouchscreentouch6startPosition.y = ctrlTouchscreentouch6startPositiony;
            ctrlTouchscreentouch7position.x = ctrlTouchscreentouch7positionx;
            ctrlTouchscreentouch7position.y = ctrlTouchscreentouch7positiony;
            ctrlTouchscreentouch7delta.up = ctrlTouchscreentouch7deltaup;
            ctrlTouchscreentouch7delta.down = ctrlTouchscreentouch7deltadown;
            ctrlTouchscreentouch7delta.left = ctrlTouchscreentouch7deltaleft;
            ctrlTouchscreentouch7delta.right = ctrlTouchscreentouch7deltaright;
            ctrlTouchscreentouch7delta.x = ctrlTouchscreentouch7deltax;
            ctrlTouchscreentouch7delta.y = ctrlTouchscreentouch7deltay;
            ctrlTouchscreentouch7radius.x = ctrlTouchscreentouch7radiusx;
            ctrlTouchscreentouch7radius.y = ctrlTouchscreentouch7radiusy;
            ctrlTouchscreentouch7startPosition.x = ctrlTouchscreentouch7startPositionx;
            ctrlTouchscreentouch7startPosition.y = ctrlTouchscreentouch7startPositiony;
            ctrlTouchscreentouch8position.x = ctrlTouchscreentouch8positionx;
            ctrlTouchscreentouch8position.y = ctrlTouchscreentouch8positiony;
            ctrlTouchscreentouch8delta.up = ctrlTouchscreentouch8deltaup;
            ctrlTouchscreentouch8delta.down = ctrlTouchscreentouch8deltadown;
            ctrlTouchscreentouch8delta.left = ctrlTouchscreentouch8deltaleft;
            ctrlTouchscreentouch8delta.right = ctrlTouchscreentouch8deltaright;
            ctrlTouchscreentouch8delta.x = ctrlTouchscreentouch8deltax;
            ctrlTouchscreentouch8delta.y = ctrlTouchscreentouch8deltay;
            ctrlTouchscreentouch8radius.x = ctrlTouchscreentouch8radiusx;
            ctrlTouchscreentouch8radius.y = ctrlTouchscreentouch8radiusy;
            ctrlTouchscreentouch8startPosition.x = ctrlTouchscreentouch8startPositionx;
            ctrlTouchscreentouch8startPosition.y = ctrlTouchscreentouch8startPositiony;
            ctrlTouchscreentouch9position.x = ctrlTouchscreentouch9positionx;
            ctrlTouchscreentouch9position.y = ctrlTouchscreentouch9positiony;
            ctrlTouchscreentouch9delta.up = ctrlTouchscreentouch9deltaup;
            ctrlTouchscreentouch9delta.down = ctrlTouchscreentouch9deltadown;
            ctrlTouchscreentouch9delta.left = ctrlTouchscreentouch9deltaleft;
            ctrlTouchscreentouch9delta.right = ctrlTouchscreentouch9deltaright;
            ctrlTouchscreentouch9delta.x = ctrlTouchscreentouch9deltax;
            ctrlTouchscreentouch9delta.y = ctrlTouchscreentouch9deltay;
            ctrlTouchscreentouch9radius.x = ctrlTouchscreentouch9radiusx;
            ctrlTouchscreentouch9radius.y = ctrlTouchscreentouch9radiusy;
            ctrlTouchscreentouch9startPosition.x = ctrlTouchscreentouch9startPositionx;
            ctrlTouchscreentouch9startPosition.y = ctrlTouchscreentouch9startPositiony;

            // State offset to control index map.
            builder.WithStateOffsetToControlIndexMap(new uint[]
            {
                32785u, 16810014u, 16810026u, 33587231u, 33587243u, 50364450u, 50364451u, 50364452u, 50364462u, 50364463u
                , 50364464u, 67141664u, 67141665u, 67141669u, 67141676u, 67141677u, 67141681u, 83918851u, 83918868u, 100696102u
                , 100696114u, 117473319u, 117473331u, 134225925u, 134225942u, 134225943u, 138420248u, 142614534u, 142614553u, 146801690u
                , 148898843u, 167837724u, 201359400u, 218136617u, 234913844u, 251691073u, 268468290u, 285245509u, 285245510u, 285245511u
                , 302022723u, 302022724u, 302022728u, 318799927u, 335577161u, 352354378u, 369107001u, 369107002u, 373301307u, 377495612u
                , 381682749u, 383779902u, 402718783u, 436240459u, 453017676u, 469794893u, 486572122u, 503349339u, 520126558u, 520126559u
                , 520126560u, 536903772u, 536903773u, 536903777u, 553680976u, 570458210u, 587235427u, 603988050u, 603988051u, 608182356u
                , 612376661u, 616563798u, 618660951u, 637599832u, 671121508u, 687898725u, 704675942u, 721453171u, 738230388u, 755007607u
                , 755007608u, 755007609u, 771784821u, 771784822u, 771784826u, 788562025u, 805339259u, 822116476u, 838869099u, 838869100u
                , 843063405u, 847257710u, 851444847u, 853542000u, 872480881u, 906002557u, 922779774u, 939556991u, 956334220u, 973111437u
                , 989888656u, 989888657u, 989888658u, 1006665870u, 1006665871u, 1006665875u, 1023443074u, 1040220308u, 1056997525u, 1073750148u
                , 1073750149u, 1077944454u, 1082138759u, 1086325896u, 1088423049u, 1107361930u, 1140883606u, 1157660823u, 1174438040u, 1191215269u
                , 1207992486u, 1224769705u, 1224769706u, 1224769707u, 1241546919u, 1241546920u, 1241546924u, 1258324123u, 1275101357u, 1291878574u
                , 1308631197u, 1308631198u, 1312825503u, 1317019808u, 1321206945u, 1323304098u, 1342242979u, 1375764655u, 1392541872u, 1409319089u
                , 1426096318u, 1442873535u, 1459650754u, 1459650755u, 1459650756u, 1476427968u, 1476427969u, 1476427973u, 1493205172u, 1509982406u
                , 1526759623u, 1543512246u, 1543512247u, 1547706552u, 1551900857u, 1556087994u, 1558185147u, 1577124028u, 1610645704u, 1627422921u
                , 1644200138u, 1660977367u, 1677754584u, 1694531803u, 1694531804u, 1694531805u, 1711309017u, 1711309018u, 1711309022u, 1728086221u
                , 1744863455u, 1761640672u, 1778393295u, 1778393296u, 1782587601u, 1786781906u, 1790969043u, 1793066196u, 1812005077u, 1845526753u
                , 1862303970u, 1879081187u, 1895858416u, 1912635633u, 1929412852u, 1929412853u, 1929412854u, 1946190066u, 1946190067u, 1946190071u
                , 1962967270u, 1979744504u, 1996521721u, 2013274344u, 2013274345u, 2017468650u, 2021662955u, 2025850092u, 2027947245u, 2046886126u
                , 2080407802u, 2097185019u, 2113962236u, 2130739465u, 2147516682u, 2164293901u, 2164293902u, 2164293903u, 2181071115u, 2181071116u
                , 2181071120u, 2197848319u, 2214625553u, 2231402770u, 2248155393u, 2248155394u, 2252349699u, 2256544004u, 2260731141u, 2262828294u
                , 2281767175u, 2315288851u, 2332066068u, 2348843285u, 2365620514u, 2382397731u, 2399174950u, 2399174951u, 2399174952u, 2415952164u
                , 2415952165u, 2415952169u, 2432729368u, 2449506602u, 2466283819u, 2483036442u, 2483036443u, 2487230748u, 2491425053u, 2495612190u
                , 2497709343u, 2516648224u, 2550169900u, 2566947117u
            });

            builder.WithControlTree(new byte[]
            {
                // Control tree nodes as bytes
                63, 19, 1, 0, 0, 0, 0, 192, 8, 3, 0, 0, 0, 0, 63, 19, 1, 1, 0, 0, 0, 128, 3, 5, 0, 0, 0, 0, 192, 8
                , 103, 0, 0, 0, 0, 192, 1, 7, 0, 0, 0, 1, 128, 3, 53, 0, 68, 0, 1, 192, 0, 9, 0, 0, 0, 0, 192, 1, 21, 0
                , 0, 0, 0, 96, 0, 11, 0, 0, 0, 0, 192, 0, 15, 0, 0, 0, 0, 32, 0, 255, 255, 1, 0, 1, 96, 0, 13, 0, 2, 0
                , 2, 64, 0, 255, 255, 4, 0, 2, 96, 0, 255, 255, 6, 0, 2, 144, 0, 17, 0, 8, 0, 8, 192, 0, 19, 0, 16, 0, 8, 120
                , 0, 255, 255, 24, 0, 6, 144, 0, 255, 255, 30, 0, 6, 168, 0, 255, 255, 36, 0, 2, 192, 0, 255, 255, 38, 0, 2, 29, 1, 23
                , 0, 0, 0, 0, 192, 1, 47, 0, 0, 0, 0, 8, 1, 25, 0, 0, 0, 0, 29, 1, 35, 0, 0, 0, 0, 228, 0, 27, 0, 40
                , 0, 4, 8, 1, 29, 0, 44, 0, 4, 210, 0, 255, 255, 48, 0, 2, 228, 0, 255, 255, 50, 0, 2, 246, 0, 255, 255, 0, 0, 0
                , 8, 1, 31, 0, 0, 0, 0, 255, 0, 255, 255, 0, 0, 0, 8, 1, 33, 0, 0, 0, 0, 4, 1, 255, 255, 52, 0, 3, 8, 1
                , 255, 255, 55, 0, 3, 24, 1, 37, 0, 0, 0, 0, 29, 1, 39, 0, 0, 0, 0, 16, 1, 255, 255, 58, 0, 1, 24, 1, 255, 255
                , 59, 0, 2, 27, 1, 41, 0, 0, 0, 0, 29, 1, 45, 0, 0, 0, 0, 26, 1, 43, 0, 0, 0, 0, 27, 1, 255, 255, 0, 0
                , 0, 25, 1, 255, 255, 61, 0, 1, 26, 1, 255, 255, 0, 0, 0, 28, 1, 255, 255, 0, 0, 0, 29, 1, 255, 255, 62, 0, 1, 128
                , 1, 49, 0, 0, 0, 0, 192, 1, 51, 0, 65, 0, 1, 79, 1, 255, 255, 63, 0, 1, 128, 1, 255, 255, 64, 0, 1, 160, 1, 255
                , 255, 66, 0, 1, 192, 1, 255, 255, 67, 0, 1, 160, 2, 55, 0, 92, 0, 1, 128, 3, 71, 0, 93, 0, 1, 48, 2, 57, 0, 77
                , 0, 4, 160, 2, 65, 0, 81, 0, 4, 248, 1, 59, 0, 71, 0, 2, 48, 2, 61, 0, 73, 0, 2, 220, 1, 255, 255, 69, 0, 1
                , 248, 1, 255, 255, 70, 0, 1, 32, 2, 63, 0, 0, 0, 0, 48, 2, 255, 255, 0, 0, 0, 12, 2, 255, 255, 75, 0, 1, 32, 2
                , 255, 255, 76, 0, 1, 96, 2, 67, 0, 0, 0, 0, 160, 2, 69, 0, 0, 0, 0, 72, 2, 255, 255, 85, 0, 3, 96, 2, 255, 255
                , 88, 0, 3, 128, 2, 255, 255, 91, 0, 1, 160, 2, 255, 255, 94, 0, 1, 16, 3, 73, 0, 105, 0, 1, 128, 3, 99, 0, 106, 0
                , 1, 216, 2, 75, 0, 0, 0, 0, 16, 3, 83, 0, 0, 0, 0, 188, 2, 255, 255, 95, 0, 1, 216, 2, 77, 0, 96, 0, 1, 200
                , 2, 79, 0, 0, 0, 0, 216, 2, 81, 0, 0, 0, 0, 194, 2, 255, 255, 97, 0, 2, 200, 2, 255, 255, 99, 0, 2, 208, 2, 255
                , 255, 101, 0, 1, 216, 2, 255, 255, 102, 0, 1, 244, 2, 85, 0, 0, 0, 0, 16, 3, 255, 255, 0, 0, 0, 230, 2, 87, 0, 0
                , 0, 0, 244, 2, 255, 255, 0, 0, 0, 223, 2, 89, 0, 0, 0, 0, 230, 2, 255, 255, 0, 0, 0, 220, 2, 91, 0, 0, 0, 0
                , 223, 2, 95, 0, 0, 0, 0, 218, 2, 93, 0, 0, 0, 0, 220, 2, 255, 255, 0, 0, 0, 217, 2, 255, 255, 103, 0, 1, 218, 2
                , 255, 255, 0, 0, 0, 222, 2, 97, 0, 0, 0, 0, 223, 2, 255, 255, 0, 0, 0, 221, 2, 255, 255, 104, 0, 1, 222, 2, 255, 255
                , 0, 0, 0, 72, 3, 255, 255, 107, 0, 2, 128, 3, 101, 0, 109, 0, 2, 100, 3, 255, 255, 111, 0, 1, 128, 3, 255, 255, 112, 0
                , 1, 0, 7, 105, 0, 0, 0, 0, 192, 8, 207, 0, 203, 0, 1, 64, 5, 107, 0, 113, 0, 1, 0, 7, 157, 0, 158, 0, 1, 96
                , 4, 109, 0, 137, 0, 1, 64, 5, 125, 0, 138, 0, 1, 240, 3, 111, 0, 122, 0, 4, 96, 4, 119, 0, 126, 0, 4, 184, 3, 113
                , 0, 116, 0, 2, 240, 3, 115, 0, 118, 0, 2, 156, 3, 255, 255, 114, 0, 1, 184, 3, 255, 255, 115, 0, 1, 224, 3, 117, 0, 0
                , 0, 0, 240, 3, 255, 255, 0, 0, 0, 204, 3, 255, 255, 120, 0, 1, 224, 3, 255, 255, 121, 0, 1, 32, 4, 121, 0, 0, 0, 0
                , 96, 4, 123, 0, 0, 0, 0, 8, 4, 255, 255, 130, 0, 3, 32, 4, 255, 255, 133, 0, 3, 64, 4, 255, 255, 136, 0, 1, 96, 4
                , 255, 255, 139, 0, 1, 208, 4, 127, 0, 150, 0, 1, 64, 5, 153, 0, 151, 0, 1, 152, 4, 129, 0, 0, 0, 0, 208, 4, 137, 0
                , 0, 0, 0, 124, 4, 255, 255, 140, 0, 1, 152, 4, 131, 0, 141, 0, 1, 136, 4, 133, 0, 0, 0, 0, 152, 4, 135, 0, 0, 0
                , 0, 130, 4, 255, 255, 142, 0, 2, 136, 4, 255, 255, 144, 0, 2, 144, 4, 255, 255, 146, 0, 1, 152, 4, 255, 255, 147, 0, 1, 180
                , 4, 139, 0, 0, 0, 0, 208, 4, 255, 255, 0, 0, 0, 166, 4, 141, 0, 0, 0, 0, 180, 4, 255, 255, 0, 0, 0, 159, 4, 143
                , 0, 0, 0, 0, 166, 4, 255, 255, 0, 0, 0, 156, 4, 145, 0, 0, 0, 0, 159, 4, 149, 0, 0, 0, 0, 154, 4, 147, 0, 0
                , 0, 0, 156, 4, 255, 255, 0, 0, 0, 153, 4, 255, 255, 148, 0, 1, 154, 4, 255, 255, 0, 0, 0, 158, 4, 151, 0, 0, 0, 0
                , 159, 4, 255, 255, 0, 0, 0, 157, 4, 255, 255, 149, 0, 1, 158, 4, 255, 255, 0, 0, 0, 8, 5, 255, 255, 152, 0, 2, 64, 5
                , 155, 0, 154, 0, 2, 36, 5, 255, 255, 156, 0, 1, 64, 5, 255, 255, 157, 0, 1, 32, 6, 159, 0, 182, 0, 1, 0, 7, 175, 0
                , 183, 0, 1, 176, 5, 161, 0, 167, 0, 4, 32, 6, 169, 0, 171, 0, 4, 120, 5, 163, 0, 161, 0, 2, 176, 5, 165, 0, 163, 0
                , 2, 92, 5, 255, 255, 159, 0, 1, 120, 5, 255, 255, 160, 0, 1, 160, 5, 167, 0, 0, 0, 0, 176, 5, 255, 255, 0, 0, 0, 140
                , 5, 255, 255, 165, 0, 1, 160, 5, 255, 255, 166, 0, 1, 224, 5, 171, 0, 0, 0, 0, 32, 6, 173, 0, 0, 0, 0, 200, 5, 255
                , 255, 175, 0, 3, 224, 5, 255, 255, 178, 0, 3, 0, 6, 255, 255, 181, 0, 1, 32, 6, 255, 255, 184, 0, 1, 144, 6, 177, 0, 195
                , 0, 1, 0, 7, 203, 0, 196, 0, 1, 88, 6, 179, 0, 0, 0, 0, 144, 6, 187, 0, 0, 0, 0, 60, 6, 255, 255, 185, 0, 1
                , 88, 6, 181, 0, 186, 0, 1, 72, 6, 183, 0, 0, 0, 0, 88, 6, 185, 0, 0, 0, 0, 66, 6, 255, 255, 187, 0, 2, 72, 6
                , 255, 255, 189, 0, 2, 80, 6, 255, 255, 191, 0, 1, 88, 6, 255, 255, 192, 0, 1, 116, 6, 189, 0, 0, 0, 0, 144, 6, 255, 255
                , 0, 0, 0, 102, 6, 191, 0, 0, 0, 0, 116, 6, 255, 255, 0, 0, 0, 95, 6, 193, 0, 0, 0, 0, 102, 6, 255, 255, 0, 0
                , 0, 92, 6, 195, 0, 0, 0, 0, 95, 6, 199, 0, 0, 0, 0, 90, 6, 197, 0, 0, 0, 0, 92, 6, 255, 255, 0, 0, 0, 89
                , 6, 255, 255, 193, 0, 1, 90, 6, 255, 255, 0, 0, 0, 94, 6, 201, 0, 0, 0, 0, 95, 6, 255, 255, 0, 0, 0, 93, 6, 255
                , 255, 194, 0, 1, 94, 6, 255, 255, 0, 0, 0, 200, 6, 255, 255, 197, 0, 2, 0, 7, 205, 0, 199, 0, 2, 228, 6, 255, 255, 201
                , 0, 1, 0, 7, 255, 255, 202, 0, 1, 224, 7, 209, 0, 227, 0, 1, 192, 8, 225, 0, 228, 0, 1, 112, 7, 211, 0, 212, 0, 4
                , 224, 7, 219, 0, 216, 0, 4, 56, 7, 213, 0, 206, 0, 2, 112, 7, 215, 0, 208, 0, 2, 28, 7, 255, 255, 204, 0, 1, 56, 7
                , 255, 255, 205, 0, 1, 96, 7, 217, 0, 0, 0, 0, 112, 7, 255, 255, 0, 0, 0, 76, 7, 255, 255, 210, 0, 1, 96, 7, 255, 255
                , 211, 0, 1, 160, 7, 221, 0, 0, 0, 0, 224, 7, 223, 0, 0, 0, 0, 136, 7, 255, 255, 220, 0, 3, 160, 7, 255, 255, 223, 0
                , 3, 192, 7, 255, 255, 226, 0, 1, 224, 7, 255, 255, 229, 0, 1, 80, 8, 227, 0, 240, 0, 1, 192, 8, 253, 0, 241, 0, 1, 24
                , 8, 229, 0, 0, 0, 0, 80, 8, 237, 0, 0, 0, 0, 252, 7, 255, 255, 230, 0, 1, 24, 8, 231, 0, 231, 0, 1, 8, 8, 233
                , 0, 0, 0, 0, 24, 8, 235, 0, 0, 0, 0, 2, 8, 255, 255, 232, 0, 2, 8, 8, 255, 255, 234, 0, 2, 16, 8, 255, 255, 236
                , 0, 1, 24, 8, 255, 255, 237, 0, 1, 52, 8, 239, 0, 0, 0, 0, 80, 8, 255, 255, 0, 0, 0, 38, 8, 241, 0, 0, 0, 0
                , 52, 8, 255, 255, 0, 0, 0, 31, 8, 243, 0, 0, 0, 0, 38, 8, 255, 255, 0, 0, 0, 28, 8, 245, 0, 0, 0, 0, 31, 8
                , 249, 0, 0, 0, 0, 26, 8, 247, 0, 0, 0, 0, 28, 8, 255, 255, 0, 0, 0, 25, 8, 255, 255, 238, 0, 1, 26, 8, 255, 255
                , 0, 0, 0, 30, 8, 251, 0, 0, 0, 0, 31, 8, 255, 255, 0, 0, 0, 29, 8, 255, 255, 239, 0, 1, 30, 8, 255, 255, 0, 0
                , 0, 136, 8, 255, 255, 242, 0, 2, 192, 8, 255, 0, 244, 0, 2, 164, 8, 255, 255, 246, 0, 1, 192, 8, 255, 255, 247, 0, 1, 0
                , 14, 3, 1, 0, 0, 0, 63, 19, 157, 1, 0, 0, 0, 64, 12, 5, 1, 0, 0, 0, 0, 14, 107, 1, 82, 1, 1, 128, 10, 7
                , 1, 248, 0, 1, 64, 12, 57, 1, 37, 1, 1, 160, 9, 9, 1, 16, 1, 1, 128, 10, 25, 1, 17, 1, 1, 48, 9, 11, 1, 1
                , 1, 4, 160, 9, 19, 1, 5, 1, 4, 248, 8, 13, 1, 251, 0, 2, 48, 9, 15, 1, 253, 0, 2, 220, 8, 255, 255, 249, 0, 1
                , 248, 8, 255, 255, 250, 0, 1, 32, 9, 17, 1, 0, 0, 0, 48, 9, 255, 255, 0, 0, 0, 12, 9, 255, 255, 255, 0, 1, 32, 9
                , 255, 255, 0, 1, 1, 96, 9, 21, 1, 0, 0, 0, 160, 9, 23, 1, 0, 0, 0, 72, 9, 255, 255, 9, 1, 3, 96, 9, 255, 255
                , 12, 1, 3, 128, 9, 255, 255, 15, 1, 1, 160, 9, 255, 255, 18, 1, 1, 16, 10, 27, 1, 29, 1, 1, 128, 10, 53, 1, 30, 1
                , 1, 216, 9, 29, 1, 0, 0, 0, 16, 10, 37, 1, 0, 0, 0, 188, 9, 255, 255, 19, 1, 1, 216, 9, 31, 1, 20, 1, 1, 200
                , 9, 33, 1, 0, 0, 0, 216, 9, 35, 1, 0, 0, 0, 194, 9, 255, 255, 21, 1, 2, 200, 9, 255, 255, 23, 1, 2, 208, 9, 255
                , 255, 25, 1, 1, 216, 9, 255, 255, 26, 1, 1, 244, 9, 39, 1, 0, 0, 0, 16, 10, 255, 255, 0, 0, 0, 230, 9, 41, 1, 0
                , 0, 0, 244, 9, 255, 255, 0, 0, 0, 223, 9, 43, 1, 0, 0, 0, 230, 9, 255, 255, 0, 0, 0, 220, 9, 45, 1, 0, 0, 0
                , 223, 9, 49, 1, 0, 0, 0, 218, 9, 47, 1, 0, 0, 0, 220, 9, 255, 255, 0, 0, 0, 217, 9, 255, 255, 27, 1, 1, 218, 9
                , 255, 255, 0, 0, 0, 222, 9, 51, 1, 0, 0, 0, 223, 9, 255, 255, 0, 0, 0, 221, 9, 255, 255, 28, 1, 1, 222, 9, 255, 255
                , 0, 0, 0, 72, 10, 255, 255, 31, 1, 2, 128, 10, 55, 1, 33, 1, 2, 100, 10, 255, 255, 35, 1, 1, 128, 10, 255, 255, 36, 1
                , 1, 96, 11, 59, 1, 61, 1, 1, 64, 12, 75, 1, 62, 1, 1, 240, 10, 61, 1, 46, 1, 4, 96, 11, 69, 1, 50, 1, 4, 184
                , 10, 63, 1, 40, 1, 2, 240, 10, 65, 1, 42, 1, 2, 156, 10, 255, 255, 38, 1, 1, 184, 10, 255, 255, 39, 1, 1, 224, 10, 67
                , 1, 0, 0, 0, 240, 10, 255, 255, 0, 0, 0, 204, 10, 255, 255, 44, 1, 1, 224, 10, 255, 255, 45, 1, 1, 32, 11, 71, 1, 0
                , 0, 0, 96, 11, 73, 1, 0, 0, 0, 8, 11, 255, 255, 54, 1, 3, 32, 11, 255, 255, 57, 1, 3, 64, 11, 255, 255, 60, 1, 1
                , 96, 11, 255, 255, 63, 1, 1, 208, 11, 77, 1, 74, 1, 1, 64, 12, 103, 1, 75, 1, 1, 152, 11, 79, 1, 0, 0, 0, 208, 11
                , 87, 1, 0, 0, 0, 124, 11, 255, 255, 64, 1, 1, 152, 11, 81, 1, 65, 1, 1, 136, 11, 83, 1, 0, 0, 0, 152, 11, 85, 1
                , 0, 0, 0, 130, 11, 255, 255, 66, 1, 2, 136, 11, 255, 255, 68, 1, 2, 144, 11, 255, 255, 70, 1, 1, 152, 11, 255, 255, 71, 1
                , 1, 180, 11, 89, 1, 0, 0, 0, 208, 11, 255, 255, 0, 0, 0, 166, 11, 91, 1, 0, 0, 0, 180, 11, 255, 255, 0, 0, 0, 159
                , 11, 93, 1, 0, 0, 0, 166, 11, 255, 255, 0, 0, 0, 156, 11, 95, 1, 0, 0, 0, 159, 11, 99, 1, 0, 0, 0, 154, 11, 97
                , 1, 0, 0, 0, 156, 11, 255, 255, 0, 0, 0, 153, 11, 255, 255, 72, 1, 1, 154, 11, 255, 255, 0, 0, 0, 158, 11, 101, 1, 0
                , 0, 0, 159, 11, 255, 255, 0, 0, 0, 157, 11, 255, 255, 73, 1, 1, 158, 11, 255, 255, 0, 0, 0, 8, 12, 255, 255, 76, 1, 2
                , 64, 12, 105, 1, 78, 1, 2, 36, 12, 255, 255, 80, 1, 1, 64, 12, 255, 255, 81, 1, 1, 32, 13, 109, 1, 106, 1, 1, 0, 14
                , 125, 1, 107, 1, 1, 176, 12, 111, 1, 91, 1, 4, 32, 13, 119, 1, 95, 1, 4, 120, 12, 113, 1, 85, 1, 2, 176, 12, 115, 1
                , 87, 1, 2, 92, 12, 255, 255, 83, 1, 1, 120, 12, 255, 255, 84, 1, 1, 160, 12, 117, 1, 0, 0, 0, 176, 12, 255, 255, 0, 0
                , 0, 140, 12, 255, 255, 89, 1, 1, 160, 12, 255, 255, 90, 1, 1, 224, 12, 121, 1, 0, 0, 0, 32, 13, 123, 1, 0, 0, 0, 200
                , 12, 255, 255, 99, 1, 3, 224, 12, 255, 255, 102, 1, 3, 0, 13, 255, 255, 105, 1, 1, 32, 13, 255, 255, 108, 1, 1, 144, 13, 127
                , 1, 119, 1, 1, 0, 14, 153, 1, 120, 1, 1, 88, 13, 129, 1, 0, 0, 0, 144, 13, 137, 1, 0, 0, 0, 60, 13, 255, 255, 109
                , 1, 1, 88, 13, 131, 1, 110, 1, 1, 72, 13, 133, 1, 0, 0, 0, 88, 13, 135, 1, 0, 0, 0, 66, 13, 255, 255, 111, 1, 2
                , 72, 13, 255, 255, 113, 1, 2, 80, 13, 255, 255, 115, 1, 1, 88, 13, 255, 255, 116, 1, 1, 116, 13, 139, 1, 0, 0, 0, 144, 13
                , 255, 255, 0, 0, 0, 102, 13, 141, 1, 0, 0, 0, 116, 13, 255, 255, 0, 0, 0, 95, 13, 143, 1, 0, 0, 0, 102, 13, 255, 255
                , 0, 0, 0, 92, 13, 145, 1, 0, 0, 0, 95, 13, 149, 1, 0, 0, 0, 90, 13, 147, 1, 0, 0, 0, 92, 13, 255, 255, 0, 0
                , 0, 89, 13, 255, 255, 117, 1, 1, 90, 13, 255, 255, 0, 0, 0, 94, 13, 151, 1, 0, 0, 0, 95, 13, 255, 255, 0, 0, 0, 93
                , 13, 255, 255, 118, 1, 1, 94, 13, 255, 255, 0, 0, 0, 200, 13, 255, 255, 121, 1, 2, 0, 14, 155, 1, 123, 1, 2, 228, 13, 255
                , 255, 125, 1, 1, 0, 14, 255, 255, 126, 1, 1, 128, 17, 159, 1, 0, 0, 0, 63, 19, 5, 2, 0, 0, 0, 192, 15, 161, 1, 127
                , 1, 1, 128, 17, 211, 1, 172, 1, 1, 224, 14, 163, 1, 151, 1, 1, 192, 15, 179, 1, 152, 1, 1, 112, 14, 165, 1, 136, 1, 4
                , 224, 14, 173, 1, 140, 1, 4, 56, 14, 167, 1, 130, 1, 2, 112, 14, 169, 1, 132, 1, 2, 28, 14, 255, 255, 128, 1, 1, 56, 14
                , 255, 255, 129, 1, 1, 96, 14, 171, 1, 0, 0, 0, 112, 14, 255, 255, 0, 0, 0, 76, 14, 255, 255, 134, 1, 1, 96, 14, 255, 255
                , 135, 1, 1, 160, 14, 175, 1, 0, 0, 0, 224, 14, 177, 1, 0, 0, 0, 136, 14, 255, 255, 144, 1, 3, 160, 14, 255, 255, 147, 1
                , 3, 192, 14, 255, 255, 150, 1, 1, 224, 14, 255, 255, 153, 1, 1, 80, 15, 181, 1, 164, 1, 1, 192, 15, 207, 1, 165, 1, 1, 24
                , 15, 183, 1, 0, 0, 0, 80, 15, 191, 1, 0, 0, 0, 252, 14, 255, 255, 154, 1, 1, 24, 15, 185, 1, 155, 1, 1, 8, 15, 187
                , 1, 0, 0, 0, 24, 15, 189, 1, 0, 0, 0, 2, 15, 255, 255, 156, 1, 2, 8, 15, 255, 255, 158, 1, 2, 16, 15, 255, 255, 160
                , 1, 1, 24, 15, 255, 255, 161, 1, 1, 52, 15, 193, 1, 0, 0, 0, 80, 15, 255, 255, 0, 0, 0, 38, 15, 195, 1, 0, 0, 0
                , 52, 15, 255, 255, 0, 0, 0, 31, 15, 197, 1, 0, 0, 0, 38, 15, 255, 255, 0, 0, 0, 28, 15, 199, 1, 0, 0, 0, 31, 15
                , 203, 1, 0, 0, 0, 26, 15, 201, 1, 0, 0, 0, 28, 15, 255, 255, 0, 0, 0, 25, 15, 255, 255, 162, 1, 1, 26, 15, 255, 255
                , 0, 0, 0, 30, 15, 205, 1, 0, 0, 0, 31, 15, 255, 255, 0, 0, 0, 29, 15, 255, 255, 163, 1, 1, 30, 15, 255, 255, 0, 0
                , 0, 136, 15, 255, 255, 166, 1, 2, 192, 15, 209, 1, 168, 1, 2, 164, 15, 255, 255, 170, 1, 1, 192, 15, 255, 255, 171, 1, 1, 160
                , 16, 213, 1, 196, 1, 1, 128, 17, 229, 1, 197, 1, 1, 48, 16, 215, 1, 181, 1, 4, 160, 16, 223, 1, 185, 1, 4, 248, 15, 217
                , 1, 175, 1, 2, 48, 16, 219, 1, 177, 1, 2, 220, 15, 255, 255, 173, 1, 1, 248, 15, 255, 255, 174, 1, 1, 32, 16, 221, 1, 0
                , 0, 0, 48, 16, 255, 255, 0, 0, 0, 12, 16, 255, 255, 179, 1, 1, 32, 16, 255, 255, 180, 1, 1, 96, 16, 225, 1, 0, 0, 0
                , 160, 16, 227, 1, 0, 0, 0, 72, 16, 255, 255, 189, 1, 3, 96, 16, 255, 255, 192, 1, 3, 128, 16, 255, 255, 195, 1, 1, 160, 16
                , 255, 255, 198, 1, 1, 16, 17, 231, 1, 209, 1, 1, 128, 17, 1, 2, 210, 1, 1, 216, 16, 233, 1, 0, 0, 0, 16, 17, 241, 1
                , 0, 0, 0, 188, 16, 255, 255, 199, 1, 1, 216, 16, 235, 1, 200, 1, 1, 200, 16, 237, 1, 0, 0, 0, 216, 16, 239, 1, 0, 0
                , 0, 194, 16, 255, 255, 201, 1, 2, 200, 16, 255, 255, 203, 1, 2, 208, 16, 255, 255, 205, 1, 1, 216, 16, 255, 255, 206, 1, 1, 244
                , 16, 243, 1, 0, 0, 0, 16, 17, 255, 255, 0, 0, 0, 230, 16, 245, 1, 0, 0, 0, 244, 16, 255, 255, 0, 0, 0, 223, 16, 247
                , 1, 0, 0, 0, 230, 16, 255, 255, 0, 0, 0, 220, 16, 249, 1, 0, 0, 0, 223, 16, 253, 1, 0, 0, 0, 218, 16, 251, 1, 0
                , 0, 0, 220, 16, 255, 255, 0, 0, 0, 217, 16, 255, 255, 207, 1, 1, 218, 16, 255, 255, 0, 0, 0, 222, 16, 255, 1, 0, 0, 0
                , 223, 16, 255, 255, 0, 0, 0, 221, 16, 255, 255, 208, 1, 1, 222, 16, 255, 255, 0, 0, 0, 72, 17, 255, 255, 211, 1, 2, 128, 17
                , 3, 2, 213, 1, 2, 100, 17, 255, 255, 215, 1, 1, 128, 17, 255, 255, 216, 1, 1, 96, 18, 7, 2, 217, 1, 2, 63, 19, 23, 2
                , 219, 1, 2, 240, 17, 9, 2, 229, 1, 4, 96, 18, 17, 2, 233, 1, 4, 184, 17, 11, 2, 223, 1, 2, 240, 17, 13, 2, 225, 1
                , 2, 156, 17, 255, 255, 221, 1, 1, 184, 17, 255, 255, 222, 1, 1, 224, 17, 15, 2, 0, 0, 0, 240, 17, 255, 255, 0, 0, 0, 204
                , 17, 255, 255, 227, 1, 1, 224, 17, 255, 255, 228, 1, 1, 32, 18, 19, 2, 0, 0, 0, 96, 18, 21, 2, 0, 0, 0, 8, 18, 255
                , 255, 237, 1, 3, 32, 18, 255, 255, 240, 1, 3, 64, 18, 255, 255, 243, 1, 1, 96, 18, 255, 255, 244, 1, 1, 208, 18, 25, 2, 255
                , 1, 1, 63, 19, 51, 2, 0, 2, 1, 152, 18, 27, 2, 0, 0, 0, 208, 18, 35, 2, 0, 0, 0, 124, 18, 255, 255, 245, 1, 1
                , 152, 18, 29, 2, 246, 1, 1, 136, 18, 31, 2, 0, 0, 0, 152, 18, 33, 2, 0, 0, 0, 130, 18, 255, 255, 247, 1, 2, 136, 18
                , 255, 255, 249, 1, 2, 144, 18, 255, 255, 251, 1, 1, 152, 18, 255, 255, 252, 1, 1, 180, 18, 37, 2, 0, 0, 0, 208, 18, 255, 255
                , 0, 0, 0, 166, 18, 39, 2, 0, 0, 0, 180, 18, 255, 255, 0, 0, 0, 159, 18, 41, 2, 0, 0, 0, 166, 18, 255, 255, 0, 0
                , 0, 156, 18, 43, 2, 0, 0, 0, 159, 18, 47, 2, 0, 0, 0, 154, 18, 45, 2, 0, 0, 0, 156, 18, 255, 255, 0, 0, 0, 153
                , 18, 255, 255, 253, 1, 1, 154, 18, 255, 255, 0, 0, 0, 158, 18, 49, 2, 0, 0, 0, 159, 18, 255, 255, 0, 0, 0, 157, 18, 255
                , 255, 254, 1, 1, 158, 18, 255, 255, 0, 0, 0, 0, 19, 255, 255, 0, 0, 0, 63, 19, 53, 2, 0, 0, 0, 32, 19, 255, 255, 1
                , 2, 2, 63, 19, 55, 2, 3, 2, 1, 48, 19, 255, 255, 4, 2, 1, 63, 19, 255, 255, 5, 2, 1
            }, new ushort[]
                {
                    // Control tree node indicies

                    0, 17, 18, 1, 30, 42, 31, 43, 19, 32, 33, 37, 2, 44, 45, 49, 19, 32, 33, 37, 2, 44, 45, 49, 34, 35, 36, 46, 47, 48
                    , 34, 35, 36, 46, 47, 48, 20, 3, 20, 3, 21, 39, 4, 51, 21, 39, 4, 51, 38, 50, 38, 50, 22, 23, 5, 22, 23, 5, 24, 25
                    , 6, 26, 27, 28, 28, 29, 40, 41, 7, 52, 52, 53, 65, 53, 65, 66, 66, 54, 69, 70, 71, 54, 69, 70, 71, 67, 68, 72, 67, 68
                    , 72, 55, 56, 56, 73, 74, 74, 57, 58, 57, 58, 59, 60, 61, 62, 63, 63, 64, 75, 64, 75, 76, 76, 8, 77, 77, 78, 90, 78, 90
                    , 91, 91, 79, 94, 95, 96, 79, 94, 95, 96, 92, 93, 97, 92, 93, 97, 80, 81, 81, 98, 99, 99, 82, 83, 82, 83, 84, 85, 86, 87
                    , 88, 88, 89, 100, 89, 100, 101, 101, 9, 102, 102, 103, 115, 103, 115, 116, 116, 104, 119, 120, 121, 104, 119, 120, 121, 117, 118, 122, 117, 118
                    , 122, 105, 106, 106, 123, 124, 124, 107, 108, 107, 108, 109, 110, 111, 112, 113, 113, 114, 125, 114, 125, 126, 126, 10, 127, 127, 128, 140, 128, 140
                    , 141, 141, 129, 144, 145, 146, 129, 144, 145, 146, 142, 143, 147, 142, 143, 147, 130, 131, 131, 148, 149, 149, 132, 133, 132, 133, 134, 135, 136, 137
                    , 138, 138, 139, 150, 139, 150, 151, 151, 11, 152, 152, 153, 165, 153, 165, 166, 166, 154, 169, 170, 171, 154, 169, 170, 171, 167, 168, 172, 167, 168
                    , 172, 155, 156, 156, 173, 174, 174, 157, 158, 157, 158, 159, 160, 161, 162, 163, 163, 164, 175, 164, 175, 176, 176, 12, 177, 177, 178, 190, 178, 190
                    , 191, 191, 179, 194, 195, 196, 179, 194, 195, 196, 192, 193, 197, 192, 193, 197, 180, 181, 181, 198, 199, 199, 182, 183, 182, 183, 184, 185, 186, 187
                    , 188, 188, 189, 200, 189, 200, 201, 201, 13, 202, 202, 203, 215, 203, 215, 216, 216, 204, 219, 220, 221, 204, 219, 220, 221, 217, 218, 222, 217, 218
                    , 222, 205, 206, 206, 223, 224, 224, 207, 208, 207, 208, 209, 210, 211, 212, 213, 213, 214, 225, 214, 225, 226, 226, 14, 227, 227, 228, 240, 228, 240
                    , 241, 241, 229, 244, 245, 246, 229, 244, 245, 246, 242, 243, 247, 242, 243, 247, 230, 231, 231, 248, 249, 249, 232, 233, 232, 233, 234, 235, 236, 237
                    , 238, 238, 239, 250, 239, 250, 251, 251, 15, 252, 252, 253, 265, 253, 265, 266, 266, 254, 269, 270, 271, 254, 269, 270, 271, 267, 268, 272, 267, 268
                    , 272, 255, 256, 256, 273, 274, 274, 257, 258, 257, 258, 259, 260, 261, 262, 263, 263, 264, 275, 264, 275, 276, 276, 16, 281, 16, 281, 277, 277, 278
                    , 290, 278, 290, 291, 291, 279, 294, 295, 296, 279, 294, 295, 296, 292, 293, 297, 292, 293, 297, 280, 298, 299, 299, 282, 283, 282, 283, 284, 285, 286
                    , 287, 288, 288, 289, 300, 289, 301, 301
                });

            builder.Finish();
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreenprimaryTouch(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouch = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreenprimaryTouch.Setup()
                .At(this, 0)
                .WithParent(parent)
                .WithChildren(17, 13)
                .WithName("primaryTouch")
                .WithDisplayName("Primary Touch")
                .WithLayout(kTouchLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 0,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreenprimaryTouch;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreenposition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreenposition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreenposition.Setup()
                .At(this, 1)
                .WithParent(parent)
                .WithChildren(42, 2)
                .WithName("position")
                .WithDisplayName("Position")
                .WithLayout(kVector2Layout)
                .WithUsages(1, 1)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 4,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                #if UNITY_EDITOR
                .WithProcessor<InputProcessor<UnityEngine.Vector2>, UnityEngine.Vector2>(new UnityEngine.InputSystem.Processors.EditorWindowSpaceProcessor())
                #endif
                .Finish();
            return ctrlTouchscreenposition;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreendelta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreendelta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreendelta.Setup()
                .At(this, 2)
                .WithParent(parent)
                .WithChildren(44, 6)
                .WithName("delta")
                .WithDisplayName("Delta")
                .WithLayout(kDeltaLayout)
                .WithUsages(2, 1)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 12,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreendelta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenpressure(InternedString kAnalogLayout, InputControl parent)
        {
            var ctrlTouchscreenpressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenpressure.Setup()
                .At(this, 3)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Pressure")
                .WithLayout(kAnalogLayout)
                .WithUsages(3, 1)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 20,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .WithDefaultState(1)
                .Finish();
            return ctrlTouchscreenpressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreenradius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreenradius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreenradius.Setup()
                .At(this, 4)
                .WithParent(parent)
                .WithChildren(50, 2)
                .WithName("radius")
                .WithDisplayName("Radius")
                .WithLayout(kVector2Layout)
                .WithUsages(4, 1)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 24,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreenradius;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreenpress(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreenpress = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreenpress.Setup()
                .At(this, 5)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Press")
                .WithLayout(kTouchPressLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 32,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreenpress;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreendisplayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreendisplayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreendisplayIndex.Setup()
                .At(this, 6)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 34,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreendisplayIndex;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch0(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch0.Setup()
                .At(this, 7)
                .WithParent(parent)
                .WithChildren(52, 13)
                .WithName("touch0")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 56,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch0;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch1(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch1.Setup()
                .At(this, 8)
                .WithParent(parent)
                .WithChildren(77, 13)
                .WithName("touch1")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 112,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch1;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch2(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch2.Setup()
                .At(this, 9)
                .WithParent(parent)
                .WithChildren(102, 13)
                .WithName("touch2")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 168,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch2;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch3(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch3.Setup()
                .At(this, 10)
                .WithParent(parent)
                .WithChildren(127, 13)
                .WithName("touch3")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 224,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch3;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch4(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch4.Setup()
                .At(this, 11)
                .WithParent(parent)
                .WithChildren(152, 13)
                .WithName("touch4")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 280,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch4;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch5(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch5.Setup()
                .At(this, 12)
                .WithParent(parent)
                .WithChildren(177, 13)
                .WithName("touch5")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 336,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch5;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch6(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch6.Setup()
                .At(this, 13)
                .WithParent(parent)
                .WithChildren(202, 13)
                .WithName("touch6")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 392,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch6;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch7(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch7.Setup()
                .At(this, 14)
                .WithParent(parent)
                .WithChildren(227, 13)
                .WithName("touch7")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 448,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch7;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch8(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch8.Setup()
                .At(this, 15)
                .WithParent(parent)
                .WithChildren(252, 13)
                .WithName("touch8")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 504,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch8;
        }

        private UnityEngine.InputSystem.Controls.TouchControl Initialize_ctrlTouchscreentouch9(InternedString kTouchLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9 = new UnityEngine.InputSystem.Controls.TouchControl();
            ctrlTouchscreentouch9.Setup()
                .At(this, 16)
                .WithParent(parent)
                .WithChildren(277, 13)
                .WithName("touch9")
                .WithDisplayName("Touch")
                .WithLayout(kTouchLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1414485315),
                    byteOffset = 560,
                    bitOffset = 0,
                    sizeInBits = 448
                })
                .Finish();
            return ctrlTouchscreentouch9;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreenprimaryTouchtouchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchtouchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreenprimaryTouchtouchId.Setup()
                .At(this, 17)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Primary Touch Touch ID")
                .WithShortDisplayName("Primary Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 0,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchtouchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreenprimaryTouchposition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchposition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreenprimaryTouchposition.Setup()
                .At(this, 18)
                .WithParent(parent)
                .WithChildren(30, 2)
                .WithName("position")
                .WithDisplayName("Primary Touch Position")
                .WithShortDisplayName("Primary Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 4,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchposition;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreenprimaryTouchdelta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchdelta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreenprimaryTouchdelta.Setup()
                .At(this, 19)
                .WithParent(parent)
                .WithChildren(32, 6)
                .WithName("delta")
                .WithDisplayName("Primary Touch Delta")
                .WithShortDisplayName("Primary Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 12,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchdelta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchpressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchpressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenprimaryTouchpressure.Setup()
                .At(this, 20)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Primary Touch Pressure")
                .WithShortDisplayName("Primary Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 20,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchpressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreenprimaryTouchradius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchradius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreenprimaryTouchradius.Setup()
                .At(this, 21)
                .WithParent(parent)
                .WithChildren(38, 2)
                .WithName("radius")
                .WithDisplayName("Primary Touch Radius")
                .WithShortDisplayName("Primary Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 24,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchradius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreenprimaryTouchphase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchphase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreenprimaryTouchphase.Setup()
                .At(this, 22)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Primary Touch Touch Phase")
                .WithShortDisplayName("Primary Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 32,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchphase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreenprimaryTouchpress(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchpress = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreenprimaryTouchpress.Setup()
                .At(this, 23)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Primary Touch Touch Contact?")
                .WithShortDisplayName("Primary Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 32,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreenprimaryTouchpress;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreenprimaryTouchtapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchtapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreenprimaryTouchtapCount.Setup()
                .At(this, 24)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Primary Touch Tap Count")
                .WithShortDisplayName("Primary Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 33,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchtapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreenprimaryTouchdisplayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchdisplayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreenprimaryTouchdisplayIndex.Setup()
                .At(this, 25)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Primary Touch Display Index")
                .WithShortDisplayName("Primary Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 34,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchdisplayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreenprimaryTouchindirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchindirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreenprimaryTouchindirectTouch.Setup()
                .At(this, 26)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Primary Touch Indirect Touch?")
                .WithShortDisplayName("Primary Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 35,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreenprimaryTouchindirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreenprimaryTouchtap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchtap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreenprimaryTouchtap.Setup()
                .At(this, 27)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Primary Touch Tap")
                .WithShortDisplayName("Primary Touch Tap")
                .WithLayout(kButtonLayout)
                .WithUsages(0, 1)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 35,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreenprimaryTouchtap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreenprimaryTouchstartTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchstartTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreenprimaryTouchstartTime.Setup()
                .At(this, 28)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Primary Touch Start Time")
                .WithShortDisplayName("Primary Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 40,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchstartTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreenprimaryTouchstartPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchstartPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreenprimaryTouchstartPosition.Setup()
                .At(this, 29)
                .WithParent(parent)
                .WithChildren(40, 2)
                .WithName("startPosition")
                .WithDisplayName("Primary Touch Start Position")
                .WithShortDisplayName("Primary Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 48,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchstartPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchpositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchpositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenprimaryTouchpositionx.Setup()
                .At(this, 30)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Primary Touch Primary Touch Position X")
                .WithShortDisplayName("Primary Touch Primary Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 4,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchpositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchpositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchpositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenprimaryTouchpositiony.Setup()
                .At(this, 31)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Primary Touch Primary Touch Position Y")
                .WithShortDisplayName("Primary Touch Primary Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 8,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchpositiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchdeltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchdeltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreenprimaryTouchdeltaup.Setup()
                .At(this, 32)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Primary Touch Primary Touch Delta Up")
                .WithShortDisplayName("Primary Touch Primary Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 16,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchdeltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchdeltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchdeltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreenprimaryTouchdeltadown.Setup()
                .At(this, 33)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Primary Touch Primary Touch Delta Down")
                .WithShortDisplayName("Primary Touch Primary Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 16,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchdeltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchdeltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchdeltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreenprimaryTouchdeltaleft.Setup()
                .At(this, 34)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Primary Touch Primary Touch Delta Left")
                .WithShortDisplayName("Primary Touch Primary Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 12,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchdeltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchdeltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchdeltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreenprimaryTouchdeltaright.Setup()
                .At(this, 35)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Primary Touch Primary Touch Delta Right")
                .WithShortDisplayName("Primary Touch Primary Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 12,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchdeltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchdeltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchdeltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenprimaryTouchdeltax.Setup()
                .At(this, 36)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Primary Touch Primary Touch Delta X")
                .WithShortDisplayName("Primary Touch Primary Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 12,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchdeltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchdeltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchdeltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenprimaryTouchdeltay.Setup()
                .At(this, 37)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Primary Touch Primary Touch Delta Y")
                .WithShortDisplayName("Primary Touch Primary Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 16,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchdeltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchradiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchradiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenprimaryTouchradiusx.Setup()
                .At(this, 38)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Primary Touch Primary Touch Radius X")
                .WithShortDisplayName("Primary Touch Primary Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 24,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchradiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchradiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchradiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenprimaryTouchradiusy.Setup()
                .At(this, 39)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Primary Touch Primary Touch Radius Y")
                .WithShortDisplayName("Primary Touch Primary Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 28,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchradiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchstartPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchstartPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenprimaryTouchstartPositionx.Setup()
                .At(this, 40)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Primary Touch Primary Touch Start Position X")
                .WithShortDisplayName("Primary Touch Primary Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 48,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchstartPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenprimaryTouchstartPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenprimaryTouchstartPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenprimaryTouchstartPositiony.Setup()
                .At(this, 41)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Primary Touch Primary Touch Start Position Y")
                .WithShortDisplayName("Primary Touch Primary Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 52,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenprimaryTouchstartPositiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenpositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenpositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenpositionx.Setup()
                .At(this, 42)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Position X")
                .WithShortDisplayName("Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 4,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenpositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenpositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenpositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenpositiony.Setup()
                .At(this, 43)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Position Y")
                .WithShortDisplayName("Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 8,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenpositiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreendeltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreendeltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreendeltaup.Setup()
                .At(this, 44)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Delta Up")
                .WithShortDisplayName("Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 16,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreendeltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreendeltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreendeltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreendeltadown.Setup()
                .At(this, 45)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Delta Down")
                .WithShortDisplayName("Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 16,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreendeltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreendeltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreendeltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreendeltaleft.Setup()
                .At(this, 46)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Delta Left")
                .WithShortDisplayName("Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 12,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreendeltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreendeltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreendeltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreendeltaright.Setup()
                .At(this, 47)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Delta Right")
                .WithShortDisplayName("Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 12,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreendeltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreendeltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreendeltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreendeltax.Setup()
                .At(this, 48)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Delta X")
                .WithShortDisplayName("Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 12,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreendeltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreendeltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreendeltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreendeltay.Setup()
                .At(this, 49)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Delta Y")
                .WithShortDisplayName("Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 16,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreendeltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenradiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenradiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenradiusx.Setup()
                .At(this, 50)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Radius X")
                .WithShortDisplayName("Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 24,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenradiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreenradiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreenradiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreenradiusy.Setup()
                .At(this, 51)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Radius Y")
                .WithShortDisplayName("Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 28,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreenradiusy;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch0touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch0touchId.Setup()
                .At(this, 52)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 56,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch0position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch0position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch0position.Setup()
                .At(this, 53)
                .WithParent(parent)
                .WithChildren(65, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 60,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch0position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch0delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch0delta.Setup()
                .At(this, 54)
                .WithParent(parent)
                .WithChildren(67, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 68,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch0delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch0pressure.Setup()
                .At(this, 55)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 76,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch0radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch0radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch0radius.Setup()
                .At(this, 56)
                .WithParent(parent)
                .WithChildren(73, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 80,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch0radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch0phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch0phase.Setup()
                .At(this, 57)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 88,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch0phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch0press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch0press.Setup()
                .At(this, 58)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 88,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch0press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch0tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch0tapCount.Setup()
                .At(this, 59)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 89,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch0tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch0displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch0displayIndex.Setup()
                .At(this, 60)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 90,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch0displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch0indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch0indirectTouch.Setup()
                .At(this, 61)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 91,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch0indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch0tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch0tap.Setup()
                .At(this, 62)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 91,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch0tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch0startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch0startTime.Setup()
                .At(this, 63)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 96,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch0startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch0startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch0startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch0startPosition.Setup()
                .At(this, 64)
                .WithParent(parent)
                .WithChildren(75, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 104,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch0startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch0positionx.Setup()
                .At(this, 65)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 60,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch0positiony.Setup()
                .At(this, 66)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 64,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch0deltaup.Setup()
                .At(this, 67)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 72,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch0deltadown.Setup()
                .At(this, 68)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 72,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch0deltaleft.Setup()
                .At(this, 69)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 68,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch0deltaright.Setup()
                .At(this, 70)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 68,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch0deltax.Setup()
                .At(this, 71)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 68,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch0deltay.Setup()
                .At(this, 72)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 72,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch0radiusx.Setup()
                .At(this, 73)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 80,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch0radiusy.Setup()
                .At(this, 74)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 84,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch0startPositionx.Setup()
                .At(this, 75)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 104,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch0startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch0startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch0startPositiony.Setup()
                .At(this, 76)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 108,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch0startPositiony;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch1touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch1touchId.Setup()
                .At(this, 77)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 112,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch1position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch1position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch1position.Setup()
                .At(this, 78)
                .WithParent(parent)
                .WithChildren(90, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 116,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch1position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch1delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch1delta.Setup()
                .At(this, 79)
                .WithParent(parent)
                .WithChildren(92, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 124,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch1delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch1pressure.Setup()
                .At(this, 80)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 132,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch1radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch1radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch1radius.Setup()
                .At(this, 81)
                .WithParent(parent)
                .WithChildren(98, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 136,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch1radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch1phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch1phase.Setup()
                .At(this, 82)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 144,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch1phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch1press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch1press.Setup()
                .At(this, 83)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 144,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch1press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch1tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch1tapCount.Setup()
                .At(this, 84)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 145,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch1tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch1displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch1displayIndex.Setup()
                .At(this, 85)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 146,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch1displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch1indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch1indirectTouch.Setup()
                .At(this, 86)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 147,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch1indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch1tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch1tap.Setup()
                .At(this, 87)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 147,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch1tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch1startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch1startTime.Setup()
                .At(this, 88)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 152,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch1startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch1startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch1startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch1startPosition.Setup()
                .At(this, 89)
                .WithParent(parent)
                .WithChildren(100, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 160,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch1startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch1positionx.Setup()
                .At(this, 90)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 116,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch1positiony.Setup()
                .At(this, 91)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 120,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch1deltaup.Setup()
                .At(this, 92)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 128,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch1deltadown.Setup()
                .At(this, 93)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 128,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch1deltaleft.Setup()
                .At(this, 94)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 124,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch1deltaright.Setup()
                .At(this, 95)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 124,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch1deltax.Setup()
                .At(this, 96)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 124,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch1deltay.Setup()
                .At(this, 97)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 128,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch1radiusx.Setup()
                .At(this, 98)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 136,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch1radiusy.Setup()
                .At(this, 99)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 140,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch1startPositionx.Setup()
                .At(this, 100)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 160,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch1startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch1startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch1startPositiony.Setup()
                .At(this, 101)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 164,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch1startPositiony;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch2touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch2touchId.Setup()
                .At(this, 102)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 168,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch2position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch2position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch2position.Setup()
                .At(this, 103)
                .WithParent(parent)
                .WithChildren(115, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 172,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch2position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch2delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch2delta.Setup()
                .At(this, 104)
                .WithParent(parent)
                .WithChildren(117, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 180,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch2delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch2pressure.Setup()
                .At(this, 105)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 188,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch2radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch2radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch2radius.Setup()
                .At(this, 106)
                .WithParent(parent)
                .WithChildren(123, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 192,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch2radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch2phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch2phase.Setup()
                .At(this, 107)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 200,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch2phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch2press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch2press.Setup()
                .At(this, 108)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 200,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch2press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch2tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch2tapCount.Setup()
                .At(this, 109)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 201,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch2tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch2displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch2displayIndex.Setup()
                .At(this, 110)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 202,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch2displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch2indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch2indirectTouch.Setup()
                .At(this, 111)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 203,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch2indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch2tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch2tap.Setup()
                .At(this, 112)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 203,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch2tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch2startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch2startTime.Setup()
                .At(this, 113)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 208,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch2startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch2startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch2startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch2startPosition.Setup()
                .At(this, 114)
                .WithParent(parent)
                .WithChildren(125, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 216,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch2startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch2positionx.Setup()
                .At(this, 115)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 172,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch2positiony.Setup()
                .At(this, 116)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 176,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch2deltaup.Setup()
                .At(this, 117)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 184,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch2deltadown.Setup()
                .At(this, 118)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 184,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch2deltaleft.Setup()
                .At(this, 119)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 180,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch2deltaright.Setup()
                .At(this, 120)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 180,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch2deltax.Setup()
                .At(this, 121)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 180,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch2deltay.Setup()
                .At(this, 122)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 184,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch2radiusx.Setup()
                .At(this, 123)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 192,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch2radiusy.Setup()
                .At(this, 124)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 196,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch2startPositionx.Setup()
                .At(this, 125)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 216,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch2startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch2startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch2startPositiony.Setup()
                .At(this, 126)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 220,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch2startPositiony;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch3touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch3touchId.Setup()
                .At(this, 127)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 224,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch3position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch3position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch3position.Setup()
                .At(this, 128)
                .WithParent(parent)
                .WithChildren(140, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 228,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch3position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch3delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch3delta.Setup()
                .At(this, 129)
                .WithParent(parent)
                .WithChildren(142, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 236,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch3delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch3pressure.Setup()
                .At(this, 130)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 244,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch3radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch3radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch3radius.Setup()
                .At(this, 131)
                .WithParent(parent)
                .WithChildren(148, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 248,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch3radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch3phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch3phase.Setup()
                .At(this, 132)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 256,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch3phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch3press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch3press.Setup()
                .At(this, 133)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 256,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch3press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch3tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch3tapCount.Setup()
                .At(this, 134)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 257,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch3tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch3displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch3displayIndex.Setup()
                .At(this, 135)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 258,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch3displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch3indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch3indirectTouch.Setup()
                .At(this, 136)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 259,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch3indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch3tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch3tap.Setup()
                .At(this, 137)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 259,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch3tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch3startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch3startTime.Setup()
                .At(this, 138)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 264,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch3startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch3startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch3startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch3startPosition.Setup()
                .At(this, 139)
                .WithParent(parent)
                .WithChildren(150, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 272,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch3startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch3positionx.Setup()
                .At(this, 140)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 228,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch3positiony.Setup()
                .At(this, 141)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 232,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch3deltaup.Setup()
                .At(this, 142)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 240,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch3deltadown.Setup()
                .At(this, 143)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 240,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch3deltaleft.Setup()
                .At(this, 144)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 236,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch3deltaright.Setup()
                .At(this, 145)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 236,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch3deltax.Setup()
                .At(this, 146)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 236,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch3deltay.Setup()
                .At(this, 147)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 240,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch3radiusx.Setup()
                .At(this, 148)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 248,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch3radiusy.Setup()
                .At(this, 149)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 252,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch3startPositionx.Setup()
                .At(this, 150)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 272,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch3startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch3startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch3startPositiony.Setup()
                .At(this, 151)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 276,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch3startPositiony;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch4touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch4touchId.Setup()
                .At(this, 152)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 280,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch4position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch4position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch4position.Setup()
                .At(this, 153)
                .WithParent(parent)
                .WithChildren(165, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 284,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch4position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch4delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch4delta.Setup()
                .At(this, 154)
                .WithParent(parent)
                .WithChildren(167, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 292,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch4delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch4pressure.Setup()
                .At(this, 155)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 300,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch4radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch4radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch4radius.Setup()
                .At(this, 156)
                .WithParent(parent)
                .WithChildren(173, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 304,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch4radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch4phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch4phase.Setup()
                .At(this, 157)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 312,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch4phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch4press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch4press.Setup()
                .At(this, 158)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 312,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch4press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch4tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch4tapCount.Setup()
                .At(this, 159)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 313,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch4tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch4displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch4displayIndex.Setup()
                .At(this, 160)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 314,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch4displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch4indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch4indirectTouch.Setup()
                .At(this, 161)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 315,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch4indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch4tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch4tap.Setup()
                .At(this, 162)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 315,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch4tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch4startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch4startTime.Setup()
                .At(this, 163)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 320,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch4startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch4startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch4startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch4startPosition.Setup()
                .At(this, 164)
                .WithParent(parent)
                .WithChildren(175, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 328,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch4startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch4positionx.Setup()
                .At(this, 165)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 284,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch4positiony.Setup()
                .At(this, 166)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 288,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch4deltaup.Setup()
                .At(this, 167)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 296,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch4deltadown.Setup()
                .At(this, 168)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 296,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch4deltaleft.Setup()
                .At(this, 169)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 292,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch4deltaright.Setup()
                .At(this, 170)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 292,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch4deltax.Setup()
                .At(this, 171)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 292,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch4deltay.Setup()
                .At(this, 172)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 296,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch4radiusx.Setup()
                .At(this, 173)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 304,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch4radiusy.Setup()
                .At(this, 174)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 308,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch4startPositionx.Setup()
                .At(this, 175)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 328,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch4startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch4startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch4startPositiony.Setup()
                .At(this, 176)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 332,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch4startPositiony;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch5touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch5touchId.Setup()
                .At(this, 177)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 336,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch5position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch5position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch5position.Setup()
                .At(this, 178)
                .WithParent(parent)
                .WithChildren(190, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 340,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch5position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch5delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch5delta.Setup()
                .At(this, 179)
                .WithParent(parent)
                .WithChildren(192, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 348,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch5delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch5pressure.Setup()
                .At(this, 180)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 356,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch5radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch5radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch5radius.Setup()
                .At(this, 181)
                .WithParent(parent)
                .WithChildren(198, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 360,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch5radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch5phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch5phase.Setup()
                .At(this, 182)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 368,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch5phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch5press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch5press.Setup()
                .At(this, 183)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 368,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch5press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch5tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch5tapCount.Setup()
                .At(this, 184)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 369,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch5tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch5displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch5displayIndex.Setup()
                .At(this, 185)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 370,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch5displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch5indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch5indirectTouch.Setup()
                .At(this, 186)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 371,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch5indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch5tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch5tap.Setup()
                .At(this, 187)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 371,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch5tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch5startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch5startTime.Setup()
                .At(this, 188)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 376,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch5startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch5startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch5startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch5startPosition.Setup()
                .At(this, 189)
                .WithParent(parent)
                .WithChildren(200, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 384,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch5startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch5positionx.Setup()
                .At(this, 190)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 340,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch5positiony.Setup()
                .At(this, 191)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 344,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch5deltaup.Setup()
                .At(this, 192)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 352,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch5deltadown.Setup()
                .At(this, 193)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 352,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch5deltaleft.Setup()
                .At(this, 194)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 348,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch5deltaright.Setup()
                .At(this, 195)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 348,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch5deltax.Setup()
                .At(this, 196)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 348,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch5deltay.Setup()
                .At(this, 197)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 352,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch5radiusx.Setup()
                .At(this, 198)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 360,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch5radiusy.Setup()
                .At(this, 199)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 364,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch5startPositionx.Setup()
                .At(this, 200)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 384,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch5startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch5startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch5startPositiony.Setup()
                .At(this, 201)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 388,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch5startPositiony;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch6touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch6touchId.Setup()
                .At(this, 202)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 392,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch6position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch6position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch6position.Setup()
                .At(this, 203)
                .WithParent(parent)
                .WithChildren(215, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 396,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch6position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch6delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch6delta.Setup()
                .At(this, 204)
                .WithParent(parent)
                .WithChildren(217, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 404,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch6delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch6pressure.Setup()
                .At(this, 205)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 412,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch6radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch6radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch6radius.Setup()
                .At(this, 206)
                .WithParent(parent)
                .WithChildren(223, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 416,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch6radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch6phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch6phase.Setup()
                .At(this, 207)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 424,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch6phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch6press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch6press.Setup()
                .At(this, 208)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 424,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch6press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch6tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch6tapCount.Setup()
                .At(this, 209)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 425,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch6tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch6displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch6displayIndex.Setup()
                .At(this, 210)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 426,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch6displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch6indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch6indirectTouch.Setup()
                .At(this, 211)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 427,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch6indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch6tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch6tap.Setup()
                .At(this, 212)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 427,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch6tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch6startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch6startTime.Setup()
                .At(this, 213)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 432,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch6startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch6startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch6startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch6startPosition.Setup()
                .At(this, 214)
                .WithParent(parent)
                .WithChildren(225, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 440,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch6startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch6positionx.Setup()
                .At(this, 215)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 396,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch6positiony.Setup()
                .At(this, 216)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 400,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch6deltaup.Setup()
                .At(this, 217)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 408,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch6deltadown.Setup()
                .At(this, 218)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 408,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch6deltaleft.Setup()
                .At(this, 219)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 404,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch6deltaright.Setup()
                .At(this, 220)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 404,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch6deltax.Setup()
                .At(this, 221)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 404,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch6deltay.Setup()
                .At(this, 222)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 408,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch6radiusx.Setup()
                .At(this, 223)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 416,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch6radiusy.Setup()
                .At(this, 224)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 420,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch6startPositionx.Setup()
                .At(this, 225)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 440,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch6startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch6startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch6startPositiony.Setup()
                .At(this, 226)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 444,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch6startPositiony;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch7touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch7touchId.Setup()
                .At(this, 227)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 448,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch7position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch7position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch7position.Setup()
                .At(this, 228)
                .WithParent(parent)
                .WithChildren(240, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 452,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch7position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch7delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch7delta.Setup()
                .At(this, 229)
                .WithParent(parent)
                .WithChildren(242, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 460,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch7delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch7pressure.Setup()
                .At(this, 230)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 468,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch7radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch7radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch7radius.Setup()
                .At(this, 231)
                .WithParent(parent)
                .WithChildren(248, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 472,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch7radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch7phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch7phase.Setup()
                .At(this, 232)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 480,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch7phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch7press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch7press.Setup()
                .At(this, 233)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 480,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch7press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch7tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch7tapCount.Setup()
                .At(this, 234)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 481,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch7tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch7displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch7displayIndex.Setup()
                .At(this, 235)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 482,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch7displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch7indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch7indirectTouch.Setup()
                .At(this, 236)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 483,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch7indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch7tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch7tap.Setup()
                .At(this, 237)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 483,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch7tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch7startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch7startTime.Setup()
                .At(this, 238)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 488,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch7startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch7startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch7startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch7startPosition.Setup()
                .At(this, 239)
                .WithParent(parent)
                .WithChildren(250, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 496,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch7startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch7positionx.Setup()
                .At(this, 240)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 452,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch7positiony.Setup()
                .At(this, 241)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 456,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch7deltaup.Setup()
                .At(this, 242)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 464,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch7deltadown.Setup()
                .At(this, 243)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 464,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch7deltaleft.Setup()
                .At(this, 244)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 460,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch7deltaright.Setup()
                .At(this, 245)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 460,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch7deltax.Setup()
                .At(this, 246)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 460,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch7deltay.Setup()
                .At(this, 247)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 464,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch7radiusx.Setup()
                .At(this, 248)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 472,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch7radiusy.Setup()
                .At(this, 249)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 476,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch7startPositionx.Setup()
                .At(this, 250)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 496,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch7startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch7startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch7startPositiony.Setup()
                .At(this, 251)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 500,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch7startPositiony;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch8touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch8touchId.Setup()
                .At(this, 252)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 504,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch8position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch8position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch8position.Setup()
                .At(this, 253)
                .WithParent(parent)
                .WithChildren(265, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 508,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch8position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch8delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch8delta.Setup()
                .At(this, 254)
                .WithParent(parent)
                .WithChildren(267, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 516,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch8delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch8pressure.Setup()
                .At(this, 255)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 524,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch8radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch8radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch8radius.Setup()
                .At(this, 256)
                .WithParent(parent)
                .WithChildren(273, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 528,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch8radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch8phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch8phase.Setup()
                .At(this, 257)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 536,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch8phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch8press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch8press.Setup()
                .At(this, 258)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 536,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch8press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch8tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch8tapCount.Setup()
                .At(this, 259)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 537,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch8tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch8displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch8displayIndex.Setup()
                .At(this, 260)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 538,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch8displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch8indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch8indirectTouch.Setup()
                .At(this, 261)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 539,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch8indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch8tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch8tap.Setup()
                .At(this, 262)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 539,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch8tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch8startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch8startTime.Setup()
                .At(this, 263)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 544,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch8startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch8startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch8startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch8startPosition.Setup()
                .At(this, 264)
                .WithParent(parent)
                .WithChildren(275, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 552,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch8startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch8positionx.Setup()
                .At(this, 265)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 508,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch8positiony.Setup()
                .At(this, 266)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 512,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch8deltaup.Setup()
                .At(this, 267)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 520,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch8deltadown.Setup()
                .At(this, 268)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 520,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch8deltaleft.Setup()
                .At(this, 269)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 516,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch8deltaright.Setup()
                .At(this, 270)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 516,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch8deltax.Setup()
                .At(this, 271)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 516,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch8deltay.Setup()
                .At(this, 272)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 520,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch8radiusx.Setup()
                .At(this, 273)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 528,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch8radiusy.Setup()
                .At(this, 274)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 532,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch8startPositionx.Setup()
                .At(this, 275)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 552,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch8startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch8startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch8startPositiony.Setup()
                .At(this, 276)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 556,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch8startPositiony;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch9touchId(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9touchId = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch9touchId.Setup()
                .At(this, 277)
                .WithParent(parent)
                .WithName("touchId")
                .WithDisplayName("Touch Touch ID")
                .WithShortDisplayName("Touch Touch ID")
                .WithLayout(kIntegerLayout)
                .IsSynthetic(true)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1229870112),
                    byteOffset = 560,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9touchId;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch9position(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch9position = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch9position.Setup()
                .At(this, 278)
                .WithParent(parent)
                .WithChildren(290, 2)
                .WithName("position")
                .WithDisplayName("Touch Position")
                .WithShortDisplayName("Touch Position")
                .WithLayout(kVector2Layout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 564,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch9position;
        }

        private UnityEngine.InputSystem.Controls.DeltaControl Initialize_ctrlTouchscreentouch9delta(InternedString kDeltaLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9delta = new UnityEngine.InputSystem.Controls.DeltaControl();
            ctrlTouchscreentouch9delta.Setup()
                .At(this, 279)
                .WithParent(parent)
                .WithChildren(292, 6)
                .WithName("delta")
                .WithDisplayName("Touch Delta")
                .WithShortDisplayName("Touch Delta")
                .WithLayout(kDeltaLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 572,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch9delta;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9pressure(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9pressure = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch9pressure.Setup()
                .At(this, 280)
                .WithParent(parent)
                .WithName("pressure")
                .WithDisplayName("Touch Pressure")
                .WithShortDisplayName("Touch Pressure")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 580,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9pressure;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch9radius(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch9radius = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch9radius.Setup()
                .At(this, 281)
                .WithParent(parent)
                .WithChildren(298, 2)
                .WithName("radius")
                .WithDisplayName("Touch Radius")
                .WithShortDisplayName("Touch Radius")
                .WithLayout(kVector2Layout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 584,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch9radius;
        }

        private UnityEngine.InputSystem.Controls.TouchPhaseControl Initialize_ctrlTouchscreentouch9phase(InternedString kTouchPhaseLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9phase = new UnityEngine.InputSystem.Controls.TouchPhaseControl();
            ctrlTouchscreentouch9phase.Setup()
                .At(this, 282)
                .WithParent(parent)
                .WithName("phase")
                .WithDisplayName("Touch Touch Phase")
                .WithShortDisplayName("Touch Touch Phase")
                .WithLayout(kTouchPhaseLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 592,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch9phase;
        }

        private UnityEngine.InputSystem.Controls.TouchPressControl Initialize_ctrlTouchscreentouch9press(InternedString kTouchPressLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9press = new UnityEngine.InputSystem.Controls.TouchPressControl();
            ctrlTouchscreentouch9press.Setup()
                .At(this, 283)
                .WithParent(parent)
                .WithName("press")
                .WithDisplayName("Touch Touch Contact?")
                .WithShortDisplayName("Touch Touch Contact?")
                .WithLayout(kTouchPressLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 592,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch9press;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch9tapCount(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9tapCount = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch9tapCount.Setup()
                .At(this, 284)
                .WithParent(parent)
                .WithName("tapCount")
                .WithDisplayName("Touch Tap Count")
                .WithShortDisplayName("Touch Tap Count")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 593,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch9tapCount;
        }

        private UnityEngine.InputSystem.Controls.IntegerControl Initialize_ctrlTouchscreentouch9displayIndex(InternedString kIntegerLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9displayIndex = new UnityEngine.InputSystem.Controls.IntegerControl();
            ctrlTouchscreentouch9displayIndex.Setup()
                .At(this, 285)
                .WithParent(parent)
                .WithName("displayIndex")
                .WithDisplayName("Touch Display Index")
                .WithShortDisplayName("Touch Display Index")
                .WithLayout(kIntegerLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1113150533),
                    byteOffset = 594,
                    bitOffset = 0,
                    sizeInBits = 8
                })
                .Finish();
            return ctrlTouchscreentouch9displayIndex;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch9indirectTouch(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9indirectTouch = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch9indirectTouch.Setup()
                .At(this, 286)
                .WithParent(parent)
                .WithName("indirectTouch")
                .WithDisplayName("Touch Indirect Touch?")
                .WithShortDisplayName("Touch Indirect Touch?")
                .WithLayout(kButtonLayout)
                .IsSynthetic(true)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 595,
                    bitOffset = 0,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch9indirectTouch;
        }

        private UnityEngine.InputSystem.Controls.ButtonControl Initialize_ctrlTouchscreentouch9tap(InternedString kButtonLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9tap = new UnityEngine.InputSystem.Controls.ButtonControl();
            ctrlTouchscreentouch9tap.Setup()
                .At(this, 287)
                .WithParent(parent)
                .WithName("tap")
                .WithDisplayName("Touch Tap")
                .WithShortDisplayName("Touch Tap")
                .WithLayout(kButtonLayout)
                .IsButton(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1112101920),
                    byteOffset = 595,
                    bitOffset = 4,
                    sizeInBits = 1
                })
                .WithMinAndMax(0, 1)
                .Finish();
            return ctrlTouchscreentouch9tap;
        }

        private UnityEngine.InputSystem.Controls.DoubleControl Initialize_ctrlTouchscreentouch9startTime(InternedString kDoubleLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9startTime = new UnityEngine.InputSystem.Controls.DoubleControl();
            ctrlTouchscreentouch9startTime.Setup()
                .At(this, 288)
                .WithParent(parent)
                .WithName("startTime")
                .WithDisplayName("Touch Start Time")
                .WithShortDisplayName("Touch Start Time")
                .WithLayout(kDoubleLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1145195552),
                    byteOffset = 600,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch9startTime;
        }

        private UnityEngine.InputSystem.Controls.Vector2Control Initialize_ctrlTouchscreentouch9startPosition(InternedString kVector2Layout, InputControl parent)
        {
            var ctrlTouchscreentouch9startPosition = new UnityEngine.InputSystem.Controls.Vector2Control();
            ctrlTouchscreentouch9startPosition.Setup()
                .At(this, 289)
                .WithParent(parent)
                .WithChildren(300, 2)
                .WithName("startPosition")
                .WithDisplayName("Touch Start Position")
                .WithShortDisplayName("Touch Start Position")
                .WithLayout(kVector2Layout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1447379762),
                    byteOffset = 608,
                    bitOffset = 0,
                    sizeInBits = 64
                })
                .Finish();
            return ctrlTouchscreentouch9startPosition;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9positionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9positionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch9positionx.Setup()
                .At(this, 290)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Position X")
                .WithShortDisplayName("Touch Touch Position X")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 564,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9positionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9positiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9positiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch9positiony.Setup()
                .At(this, 291)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Position Y")
                .WithShortDisplayName("Touch Touch Position Y")
                .WithLayout(kAxisLayout)
                .DontReset(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 568,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9positiony;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9deltaup(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9deltaup = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch9deltaup.Setup()
                .At(this, 292)
                .WithParent(parent)
                .WithName("up")
                .WithDisplayName("Touch Touch Delta Up")
                .WithShortDisplayName("Touch Touch Delta Up")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 576,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9deltaup;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9deltadown(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9deltadown = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch9deltadown.Setup()
                .At(this, 293)
                .WithParent(parent)
                .WithName("down")
                .WithDisplayName("Touch Touch Delta Down")
                .WithShortDisplayName("Touch Touch Delta Down")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 576,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9deltadown;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9deltaleft(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9deltaleft = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMin = -3.402823E+38f, invert = true };
            ctrlTouchscreentouch9deltaleft.Setup()
                .At(this, 294)
                .WithParent(parent)
                .WithName("left")
                .WithDisplayName("Touch Touch Delta Left")
                .WithShortDisplayName("Touch Touch Delta Left")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 572,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9deltaleft;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9deltaright(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9deltaright = new UnityEngine.InputSystem.Controls.AxisControl { clamp = UnityEngine.InputSystem.Controls.AxisControl.Clamp.BeforeNormalize, clampMax = 3.402823E+38f };
            ctrlTouchscreentouch9deltaright.Setup()
                .At(this, 295)
                .WithParent(parent)
                .WithName("right")
                .WithDisplayName("Touch Touch Delta Right")
                .WithShortDisplayName("Touch Touch Delta Right")
                .WithLayout(kAxisLayout)
                .IsSynthetic(true)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 572,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9deltaright;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9deltax(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9deltax = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch9deltax.Setup()
                .At(this, 296)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Delta X")
                .WithShortDisplayName("Touch Touch Delta X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 572,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9deltax;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9deltay(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9deltay = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch9deltay.Setup()
                .At(this, 297)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Delta Y")
                .WithShortDisplayName("Touch Touch Delta Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 576,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9deltay;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9radiusx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9radiusx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch9radiusx.Setup()
                .At(this, 298)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Radius X")
                .WithShortDisplayName("Touch Touch Radius X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 584,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9radiusx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9radiusy(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9radiusy = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch9radiusy.Setup()
                .At(this, 299)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Radius Y")
                .WithShortDisplayName("Touch Touch Radius Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 588,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9radiusy;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9startPositionx(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9startPositionx = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch9startPositionx.Setup()
                .At(this, 300)
                .WithParent(parent)
                .WithName("x")
                .WithDisplayName("Touch Touch Start Position X")
                .WithShortDisplayName("Touch Touch Start Position X")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 608,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9startPositionx;
        }

        private UnityEngine.InputSystem.Controls.AxisControl Initialize_ctrlTouchscreentouch9startPositiony(InternedString kAxisLayout, InputControl parent)
        {
            var ctrlTouchscreentouch9startPositiony = new UnityEngine.InputSystem.Controls.AxisControl();
            ctrlTouchscreentouch9startPositiony.Setup()
                .At(this, 301)
                .WithParent(parent)
                .WithName("y")
                .WithDisplayName("Touch Touch Start Position Y")
                .WithShortDisplayName("Touch Touch Start Position Y")
                .WithLayout(kAxisLayout)
                .WithStateBlock(new InputStateBlock
                {
                    format = new FourCC(1179407392),
                    byteOffset = 612,
                    bitOffset = 0,
                    sizeInBits = 32
                })
                .Finish();
            return ctrlTouchscreentouch9startPositiony;
        }
    }
}
